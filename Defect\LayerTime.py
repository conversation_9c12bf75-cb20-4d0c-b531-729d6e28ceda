from logging import exception
import numpy as np
from GcodeParse.ModelInfo import TrackInfo, ModelInfo, MotionType
import math
from Display.visualization_data import DefectVisualization
import matplotlib.pyplot as plt

def calc_track_time(track: TrackInfo,next_track: TrackInfo, v_st: float) -> tuple[float,float]:
    """
    计算轨迹打印时间
    运动学参考：https://www.klipper3d.org/zh/Kinematics.html
    Units: a - mm/s^2; v - mm/min; l - mm

    :param track: 计算时间的运动轨迹
    :param next_track: 下一条运动轨迹
    :param v_st: 当前热端的运动速度
    :return: 打印时间,结束速度
    """
    #1. 不考虑加减速
    # track.time=track.length/track.speed*60

    #2. 考虑加减速，但是从0到0 
    # l_acc=track.speed * track.speed/ track.accel /3600
    # if track.length <= l_acc:
    #     track.time=math.sqrt(4*track.length/track.accel)
    # else:
    #     track.time = 2* track.speed/ track.accel/60 + (track.length - l_acc)/ track.speed *60

    #3. 引入速度预计算，起始与终点速度不为0
    error_junc = 0.005 #允许圆弧与直线拐角误差 mm
    k_acc=0.7 #之字平滑预计算使用的加速度比例

    if track.motionType == MotionType.G1:
        vec_in = np.array(track.stPos)-np.array(track.endPos)
    elif track.motionType == MotionType.G2:
        vec_in = - np.cross(np.array([0, 0, -1]),np.array(track.endPos) - np.array(track.cicPos))
    elif track.motionType == MotionType.G3:
        vec_in = - np.cross(np.array([0, 0, 1]), np.array(track.endPos) - np.array(track.cicPos))
    elif track.motionType == MotionType.pause:
        track.time = track.ex/track.speed * 60 if track.ex > 0 and track.speed > 0 else 0
        return track.time, 0
    else:
        raise TypeError("not supported MotionType of track")

    if  next_track is None:
        vec_out = np.array([0, 0, 0])
    elif next_track.motionType == MotionType.G1:
        vec_out = np.array(next_track.endPos)-np.array(next_track.stPos)
    elif next_track.motionType == MotionType.G2:
        vec_out = np.cross(np.array([0, 0, -1]),np.array(next_track.stPos) - np.array(next_track.cicPos))
    elif next_track.motionType == MotionType.G3:
        vec_out = np.cross(np.array([0, 0, 1]), np.array(next_track.stPos) - np.array(next_track.cicPos))
    elif next_track.motionType == MotionType.pause:
        vec_out = np.array([0, 0, 0])
    else:
        raise TypeError("not supported MotionType of next_track")


    magnitude_in=np.linalg.norm(vec_in)
    magnitude_out=np.linalg.norm(vec_out)
    if magnitude_in !=0 and magnitude_out !=0:
        cosT = np.dot(vec_in, vec_out) / (magnitude_in * magnitude_out)
        cosT = np.clip(cosT, -1.0, 1.0)  # 限制在 [-1, 1] 范围内，避免浮点误差
        sinT_2 = math.sqrt((1-cosT)/2)
        if 1 - sinT_2 < 1e-4:
            R = 1e4
        else:
            R = error_junc * sinT_2 / (1 - sinT_2)
        v_junction = math.sqrt(R * track.accel)* 60
        v_end = min([track.speed, next_track.speed, v_junction])
    else:
        v_end = 0

    if track.accel != 0:

        l_acc = (track.speed + v_st) * (track.speed - v_st) / track.accel / 3600 / 2 + \
                  (track.speed + v_end) * (track.speed - v_end) / track.accel / 3600 / 2
        l_acc_k = l_acc/ k_acc
    else:
        raise ValueError("track.accel is not recorded")

    if track.length <= l_acc:
        a = track.accel/4
        b = (v_st+v_end)/2/60
        c = -(v_end-v_st)*(v_end-v_st)/track.accel/2/3600-track.length
        Delta = b*b-4*a*c
        if Delta >= 0:
            track.time = (-b + math.sqrt(Delta)) / (2 * a)
        else:
            print("error in calculate track.time")
            return 0, 0
    else:
        track.time = (2* track.speed - v_end- v_st)/track.accel/60+(track.length - l_acc)/ track.speed *60

    return track.time, v_end

def add_model_layer_time(model: ModelInfo) -> list:
    """
    输入模型，遍历更新模型每层(打印前准备阶段除外）的打印时间，以及每个轨迹的打印时间

    :param model: 模型
    :return: 模型所有层时间
    """
    current_vel=0

    for layer in model.layers[1:]:
        layer.time = 0
        for i,track in enumerate(layer.tracks):
            if i < len(layer.tracks)-1:
                _, current_vel = calc_track_time(track,layer.tracks[i+1], current_vel)
            elif i == len(layer.tracks) -1 :
                _, current_vel = calc_track_time(track,None, current_vel)
            layer.time+=track.time
        tracks_time=[track.time for track in layer.tracks]
    return [layer.time for layer in model.layers]

def plot_layer_time(axes: np.ndarray, layers_time: list[float], defect_layers_num: list[int]):
    """
    Plots layer time progression and distribution on two separate subplots.

    Args:
        axes: A tuple of two matplotlib axes objects (ax1, ax2).
        layers_time: A list of printing times for each layer.
        defect_layers_num: A list of indices for layers identified as having anomalous times.
    """
    if len(axes) != 2:
        raise ValueError("This function requires exactly two axes objects.")
    
    ax1, ax2 = axes
    
    # --- Subplot 1: Layer Time vs. Layer Number (Line Plot) ---
    ax1.clear()
    layer_indices = range(len(layers_time))
    ax1.plot(layer_indices, layers_time, marker='.', linestyle='-', markersize=4, label='Layer Time')
    ax1.set_title("Layer Time Progression")
    ax1.set_xlabel("Layer Number")
    ax1.set_ylabel("Layer Time (s)")
    ax1.grid(True, linestyle=':')
    
    # Highlight defect layers on the line plot with red dots
    if defect_layers_num:
        defect_times_on_plot = [layers_time[i] for i in defect_layers_num]
        ax1.scatter(defect_layers_num, defect_times_on_plot, color='red', s=40, zorder=5, label='Anomalous Layer')
    
    ax1.legend()
    ax1.set_xlim(left=0)

    # --- Subplot 2: Layer Time Distribution (Histogram) ---
    ax2.clear()
    # Filter out layers with no printing time (e.g., setup layer 0) for a cleaner distribution
    valid_layer_times = [t for t in layers_time if t > 1e-6]
    if not valid_layer_times:
        ax2.text(0.5, 0.5, 'No layer time data to display.', ha='center', va='center')
        ax2.set_title("Layer Time Distribution")
        return

    ax2.hist(valid_layer_times, bins=30, color='skyblue', edgecolor='black')
    ax2.set_title("Layer Time Distribution")
    ax2.set_xlabel("Per-Layer Time (s)")
    ax2.set_ylabel("Number of Layers (Frequency)")

    # Highlight the times of layers with significant time differences
    defect_times = {layers_time[i] for i in defect_layers_num if i < len(layers_time)}
    # for i, time in enumerate(sorted(list(defect_times))):
    #     label = 'Anomalous Time' if i == 0 else ''
    #     ax2.axvline(x=time, color='r', linestyle='--', linewidth=1.2, label=label)

    if defect_times:
        ax2.legend()
    ax2.grid(axis='y', alpha=0.75, linestyle=':')


def analysis_layer_time_diff(model: ModelInfo) -> DefectVisualization:
    """
    Analyzes if there is a sudden change in the time between adjacent layers.
    The analysis result is a set of layer style modifications.

    Args:
        model: The model to be analyzed.

    Returns:
        A DefectVisualization object containing layer style modifications for defective layers.
    """
    add_model_layer_time(model)
    vis = DefectVisualization()

    thre_time_diff = 25
    thre_time_diff_rel = 0.4

    for i in range(2, len(model.layers)):
        layer = model.layers[i]
        under_layer = model.layers[i-1]
        if under_layer.time == 0:
            print(f"layer {i-1} time is 0")
            continue
        else:
            if abs(layer.time - under_layer.time) > thre_time_diff and abs(layer.time - under_layer.time)/under_layer.time >= thre_time_diff_rel:
                # This instruction tells the plotter to color the entire layer red.
                vis.layer_styles[i] = {'color': 'red'}

    # Plot layer time analysis in a new window with two subplots
    fig, axes = plt.subplots(2, 1, figsize=(10, 8), constrained_layout=True)
    total_time_sec = sum(layer.time for layer in model.layers)
    fig.suptitle(f"Layer Time Analysis (Total Printing: {total_time_sec:.0f}s)", fontsize=16)
    plot_layer_time(axes, [layer.time for layer in model.layers], list(vis.layer_styles.keys()))
    fig.show()  # Use fig.show() for non-blocking display in GUI apps

    return vis

