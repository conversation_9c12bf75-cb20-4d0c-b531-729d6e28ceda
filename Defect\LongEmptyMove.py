from typing import List, Dict
from GcodeParse.ModelInfo import ModelInfo, LineType
from Display.visualization_data import DefectVisualization, Drawable


def analysis_long_empMove(model: ModelInfo, max_dist: float) -> List[DefectVisualization]:
    """
    Analyzes the model for long empty moves and returns them as visualization data.

    Args:
        model: The ModelInfo object to analyze.
        max_dist: The minimum length to be considered a long empty move.

    Returns:
        A list of DefectVisualization objects, each for a layer with long empty moves.
    """
    vis_by_layer: Dict[int, DefectVisualization] = {}
    for layer in model.layers:
        for track in layer.tracks:
            if track.lineType == LineType.Empty_move and track.length >= max_dist:
                layer_id = track.layer_id
                if layer_id not in vis_by_layer:
                    vis_by_layer[layer_id] = DefectVisualization(metadata={'layer_id': layer_id})

                # For 3D, we color the track. For 2D, this will be ignored but doesn't hurt.
                drawable = Drawable(type='track', geometry=track, style={'color': '#B91212'})
                vis_by_layer[layer_id].drawables.append(drawable)
    return list(vis_by_layer.values())
