from typing import Dict, List
from shapely.ops import unary_union
from shapely.geometry import Polygon
from GcodeParse.PolygonUtility import continious_line_with_variable_width
from GcodeParse.ModelInfo import LayerInfo, LineType, ModelInfo, MotionType
from Display.visualization_data import DefectVisualization, Drawable
from rtree import index
from Defect.Filters import find_sharp_corners

not_support_LineType = [
    LineType.Gap_infill,
    LineType.Sparse_infill,
    LineType.Bridge,
    LineType.Custom,
    LineType.Empty_move,
    LineType.Wipe,
    LineType.Floating_vertical_shell,
    LineType.Internal_solid_infill,
    LineType.Support_transition
]

def _add_overhangs_to_visualization(vis: DefectVisualization, overhang_segments: List[Dict]):
    """Helper function to add overhang drawable objects to a visualization."""
    for overhang_segment in overhang_segments:
        track = overhang_segment['track']
        intersection = overhang_segment.get('support_intersection') # Use .get for safety

        # Add the main defect track
        vis.drawables.append(Drawable(type='track', geometry=track, style={'color': 'red', 'linewidth': 2}))

        # Add the intersection polygon if it exists
        if intersection:
            vis.drawables.append(Drawable(type='polygon_2d', geometry=intersection, style={'facecolor': 'red', 'edgecolor': 'darkgreen', 'alpha': 0.6, 'zorder': 15}))

        # Add the text annotation for the overlap ratio
        if 'overlap_ratio' in overhang_segment:
            text_pos = (track.stPos[0], track.stPos[1] + 0.5)
            vis.drawables.append(Drawable(type='text_annotation', geometry=text_pos,
                                          text=f"Ratio: {overhang_segment['overlap_ratio']:.2f}",
                                          style={'fontsize': 8, 'ha': 'center', 'color': 'red',
                                                 'bbox': dict(facecolor='white', edgecolor='red', pad=0.3), 'zorder': 16}))

def overhang_detection(model: ModelInfo, min_overlap_ratio: float = 0.5, min_overhang_length: float = 3) -> List[DefectVisualization]:
    """
    Detect overhang defects by analyzing layer-to-layer wall overlap.

    Args:
        model: ModelInfo object containing layer and track information
        min_overlap_ratio: Minimum overlap ratio to consider as sufficient support
        min_overhang_length: Minimum continuous length to be considered an overhang defect.

    Returns: A list of DefectVisualization objects, each representing a continuous overhang defect.
    """
    # Use a dictionary to group all defects for a single layer into one visualization object.
    vis_by_layer: Dict[int, DefectVisualization] = {}
    least_refresh_support_length = 2

    # Initialize with empty support for the first layer
    under_layer_support_rtree = index.Index()
    under_layer_support_polygons = []

    for layer_id, layer in enumerate(model.layers):
        if layer_id % 10 == 0:
            print(f"Layer {layer_id} overhang defect analysis started")

        current_cumulative_overhang_length = 0
        current_cumulative_support_length = 0
        candidate_overhang_list = []

        # Layer 0 is on the print bed, so it has full support. We only need to calculate its support area for the next layer.
        if layer_id == 0:
            continue
        else:
            id = layer_id - 1
            under_layer = model.layers[id]
            while under_layer.check_is_support_layer():
                id -= 1
                if id >= 0:
                    under_layer = model.layers[id]
                else:
                    print("No support layer found, use layer[0]")
                    break
            under_layer_support_rtree, under_layer_support_polygons = get_continious_line_polygons_in_layer(under_layer)

        for _, track in enumerate(layer.tracks):
            # Analyze outer wall tracks(including overhang wall) for overhangs
            if track.lineType not in [LineType.Overhang_wall, LineType.Outer_wall]:
                continue

            # Build polygon for overhang wall and outer wall, and calculate overlap ratio and intersection area            
            if track.lineType == LineType.Overhang_wall:
                overlap_ratio, intersection = (0, None)
            elif track.lineType == LineType.Outer_wall:
                polygon = track.build_track_width_polygon()
                overlap_ratio, intersection = get_polygon_overlap_from_rtree(polygon, under_layer_support_rtree,
                                                                        under_layer_support_polygons)

            # No overlap, skip to next track            
            if overlap_ratio is None:
                continue

            # Continious overlap check
            if overlap_ratio < min_overlap_ratio:             
                overhang_segment = {
                    "track": track,
                    "g_code_line_num": track.codeLineNum,
                    "overlap_ratio": overlap_ratio,
                    "support_intersection": intersection
                }
                candidate_overhang_list.append(overhang_segment)
                current_cumulative_overhang_length += track.length

            # After finding an overhang track, starct to accumulate support length
            elif candidate_overhang_list:                 
                current_cumulative_support_length += track.length
                # Check after getting to a place support enough
                if current_cumulative_support_length > least_refresh_support_length:
                    #Check whether overhang segments have a sharp angle < 150 degree
                    overhang_tracks = [overhang_segment['track'] for overhang_segment in candidate_overhang_list]
                    sharp_angles = find_sharp_corners(overhang_tracks, 100, 1.0)

                    if sharp_angles or current_cumulative_overhang_length > min_overhang_length:
                        # Get or create the visualization object for this layer
                        if layer_id not in vis_by_layer:
                            vis_by_layer[layer_id] = DefectVisualization(metadata={'layer_id': layer_id})
                        vis = vis_by_layer[layer_id]
                        _add_overhangs_to_visualization(vis, candidate_overhang_list)
                    current_cumulative_support_length = 0
                    current_cumulative_overhang_length = 0
                    candidate_overhang_list = []
        
        # Check after completing one layer
        if current_cumulative_overhang_length > min_overhang_length:
            # Get or create the visualization object for this layer
            if layer_id not in vis_by_layer:
                vis_by_layer[layer_id] = DefectVisualization(metadata={'layer_id': layer_id})
            vis = vis_by_layer[layer_id]
            _add_overhangs_to_visualization(vis, candidate_overhang_list)

    return list(vis_by_layer.values())
    
    
def get_polygon_overlap_from_rtree(polygon: Polygon, support_rtree: index.Index, support_polygons: List[Polygon]):
    """
    Calculate the overlap ratio and intersection area between a polygon and a set of support polygons using an R-tree for efficient spatial queries.
    
    Return:
        overlap_ratio: overlap area / polygon area
        intersection: intersection polygon
    """
    if not polygon or polygon.is_empty:
        return 0, None

    # Find candidate support polygons using R-tree
    candidate_indices = list(support_rtree.intersection(polygon.bounds))
    if not candidate_indices:
        return 0, None
    
    # Optimization: Intersect with each candidate individually first, then union the small results.
    # This is much faster than creating a large union of all candidates and then intersecting.
    intersections = []
    for i in candidate_indices:
        # A precise intersection check is needed since the R-tree uses bounding boxes.
        intersection = polygon.intersection(support_polygons[i])
        if not intersection.is_empty:
            intersections.append(intersection)

    if not intersections:
        return 0, None

    total_intersection = unary_union(intersections)
    if not total_intersection.is_empty:
        overlap_ratio = total_intersection.area / polygon.area
        return overlap_ratio, total_intersection
    else:
        return 0, None

def _build_and_add_polygon(pts: List, widths: List, polygon_list: List):
    """
    Helper to build polygons from a continuous line segment and add them to the list.
    """
    if len(pts) < 2:
        return

    # This function now robustly returns a list of polygons, or None.
    generated_polygons = continious_line_with_variable_width(pts, widths)

    if generated_polygons:
        polygon_list.extend(generated_polygons)

def get_continious_line_polygons_in_layer(layer: LayerInfo):
    continious_line_pts = []
    continious_line_widths = []
    r_tree = index.Index()
    polygon_list = []

    for track in layer.tracks:
        is_support_track = track.lineType is not None and track.lineType not in not_support_LineType and track.ex > 0
        
        if not is_support_track:
            # End of a continuous segment, build the polygon for it
            _build_and_add_polygon(continious_line_pts, continious_line_widths, polygon_list)
            # Reset for the next segment
            continious_line_pts = []
            continious_line_widths = []
            continue
        
        # If starting a new continuous line, add the start point
        if not continious_line_pts:
            continious_line_pts.append(track.stPos[:2])
            continious_line_widths.append(track.width)

        # Add the subsequent points of the current track
        if track.motionType == MotionType.G1:
            continious_line_pts.append(track.endPos[:2])
            continious_line_widths.append(track.width)
        elif track.motionType in [MotionType.G2, MotionType.G3]:
            # For arcs, extend with the sampled points, skipping the first one
            # as it's the same as the previous track's end point.
            arc_points = track.get_arc_sample_paras()['sample_points']
            continious_line_pts.extend(arc_points[1:])
            continious_line_widths.extend([track.width] * (len(arc_points) - 1))

    # After the loop, process any remaining continuous segment
    _build_and_add_polygon(continious_line_pts, continious_line_widths, polygon_list)

    for i, polygon in enumerate(polygon_list):
        r_tree.insert(i, polygon.bounds)

    return r_tree, polygon_list
    
