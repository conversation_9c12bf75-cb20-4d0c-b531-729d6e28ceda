from typing import Dict, List, Optional, Tu<PERSON>, TYPE_CHECKING
from GcodeParse.ModelInfo import ModelInfo, LineType
from Defect.Filters import find_sharp_corners, filter_by_proximity, filter_by_density
from shapely.geometry import Point, Polygon
from shapely.ops import unary_union
from rtree import index
from Display.visualization_data import DefectVisualization, Drawable

if TYPE_CHECKING:
    from GcodeParse.ModelInfo import LayerInfo


def overlap_detection(model: ModelInfo,
                      detect_radius=1.0,
                      adjust_ratio=0.0,
                      use_corner_filter: bool = True,
                      use_density_filter: bool = True,
                      corner_angle_threshold=60.0, # For the outer wall filter
                      inner_corner_detection_angle=135.0, # For inner wall detection
                      corner_distance_threshold=1.0,
                      corner_look_ahead_dist=1.5,
                      min_cluster_size=3,
                      cluster_distance=3.0) -> List[DefectVisualization]:
    """
    Detect overlap defects in 3D printing model based on inner wall contours.

    Args:
        model: ModelInfo object containing layer and track information
        detect_radius: Radius of the circle at the corner to collect adjacent tracks.
        adjust_ratio: Circle area proportional to the detect circle area judges if it is an overlap defect.
        use_corner_filter: If True, enables filtering of defects near sharp corners.
        use_density_filter: If True, enables filtering of isolated, non-clustered defects.
        corner_angle_threshold: For the outer wall filter. Defects near outer corners sharper than this will be ignored.
        inner_corner_detection_angle: For inner wall detection. Inner corners sharper than this will be checked for defects.
        corner_distance_threshold: The distance threshold to ignore defects near sharp outer corners.
        corner_look_ahead_dist: The distance to look forward/backward to calculate corner angles, for filleted corners.
        min_cluster_size: The minimum number of points required to form a dense defect cluster.
        cluster_distance: The maximum distance between defect points to be considered a cluster.

    Returns: A list of DefectVisualization objects, each representing one overlap defect.
    """
    all_potential_defects = []

    # --- STEP 1: Collect all potential defects and apply layer-specific corner filter ---
    for layer_id, layer in enumerate(model.layers):
        # Detect overlaps of inner walls and gap infill within outer wall
        if layer_id % 10 == 0:
            print(f"Layer {layer_id} overlap defect analysis started")
        
        candidate_tracks = _collect_candidate_tracks_for_layer(layer)
        if not candidate_tracks:
            continue

        # Detect overlaps
        layer_overlaps = detect_inner_wall_overlap(candidate_tracks,
                                                   out_wall_closed_region=layer.get_outer_wall_closed_region(),
                                                   in_out_wall_relations=layer.wall_and_inclusions['in_out_inclusion_map'],
                                                   detect_radius=detect_radius,
                                                   adjust_ratio=adjust_ratio,
                                                   corner_angle_threshold=inner_corner_detection_angle,
                                                   corner_look_ahead_dist=corner_look_ahead_dist)
        if not layer_overlaps:
            continue
        
        # Apply corner filter
        defects_to_add = layer_overlaps
        if use_corner_filter:
            outer_wall_contours = layer.outer_wall_countour_list
            sharp_corners = []
            for outer_wall_contour in outer_wall_contours:
                pts = find_sharp_corners(
                    outer_wall_contour,
                    angle_threshold_deg=corner_angle_threshold,
                    look_ahead_dist=corner_look_ahead_dist
                )
                if pts:
                    sharp_corners.extend(pts)
            
            if sharp_corners:
                # Get the set of points that pass the proximity filter
                passed_points_set = set(filter_by_proximity(
                    points_to_filter=[d['detect_point'] for d in defects_to_add],
                    reference_points=sharp_corners,
                    distance_threshold=corner_distance_threshold
                ))
                defects_to_add = [d for d in defects_to_add if d['detect_point'] in passed_points_set]

        # Add layer_id to each defect and append to the global list
        for defect in defects_to_add:
            defect['layer_id'] = layer_id
            all_potential_defects.append(defect)

    # --- STEP 2: Apply global 3D density filter ---
    if not use_density_filter or not all_potential_defects:
        final_defects = all_potential_defects
    else:
        # Filter the points based on 3D density
        clustered_points_set = set(filter_by_density(
            points=[d['detect_point'] for d in all_potential_defects],
            min_cluster_size=min_cluster_size,
            max_distance=cluster_distance
        ))
        # Keep only the defect dictionaries that are part of a 3D cluster
        final_defects = [d for d in all_potential_defects if d['detect_point'] in clustered_points_set]

    # --- STEP 3: Generate visualizations for the final list of defects ---
    return _create_defect_visualizations(final_defects)


def _collect_candidate_tracks_for_layer(layer: 'LayerInfo') -> List[Dict]:
    """Collects inner wall and gap infill tracks for overlap detection in a single layer."""
    if not hasattr(layer, 'wall_and_inclusions'):
        layer.build_wall_relations()
    layer_wall_info = layer.wall_and_inclusions

    if not layer_wall_info['inner_polygons']:
        return []

    candidate_tracks = []
    # Add inner wall tracks
    for contour in layer_wall_info['inner_polygons']:
        for track in contour['contour']:
            polygon = track.build_track_width_polygon(use_equivalent=True)
            if polygon and polygon.is_valid and not polygon.is_empty:
                candidate_tracks.append({
                    'track': track,
                    'polygon': polygon,
                    'wall_id': contour['id']
                })

    # Add infill tracks that can cause overlaps
    # infill_types_to_check = [LineType.Gap_infill, LineType.Internal_solid_infill]
    infill_types_to_check = [LineType.Gap_infill]
    for infill_type in infill_types_to_check:
        infill_list = layer.extract_one_lineType(infill_type)
        for track in infill_list:
            polygon = track.build_track_width_polygon(use_equivalent=True)
            if polygon and polygon.is_valid and not polygon.is_empty:
                candidate_tracks.append({
                    'track': track,
                    'polygon': polygon,
                    'wall_id': f"{infill_type.name}_0"
                })
    return candidate_tracks


def _create_defect_visualizations(final_defects: List[Dict]) -> List[DefectVisualization]:
    """Generates DefectVisualization objects from a list of defect data."""
    all_defect_visualizations = []
    for defect in final_defects:
        vis = DefectVisualization(metadata={'layer_id': defect['layer_id'], 'g_code_line': defect['g_code_line_num']})
        vis.drawables.append(Drawable(type='point', geometry=defect['detect_point'], style={'color': 'red', 's': 60, 'marker': 'x', 'zorder': 10}))
        vis.drawables.append(Drawable(type='polygon_2d', geometry=defect['material_region'], style={'facecolor': 'lightgray', 'edgecolor': 'gray', 'alpha': 0.4, 'zorder': 14}))
        vis.drawables.append(Drawable(type='polygon_2d', geometry=defect['overlap_region'], style={'facecolor': 'red', 'edgecolor': 'darkred', 'alpha': 0.6, 'zorder': 15}))
        vis.drawables.append(Drawable(type='polygon_2d', geometry=defect['detect_region'], style={'facecolor': 'none', 'edgecolor': 'purple', 'alpha': 0.8, 'zorder': 16}))
        annotation_text = f"V_ovlp = {defect['overlap_volumn']:.3f} mm^3"
        text_pos = (defect['detect_point'][0], defect['detect_point'][1] + 0.5)
        vis.drawables.append(Drawable(type='text_annotation', geometry=text_pos, text=annotation_text, style={'fontsize': 8, 'ha': 'center', 'bbox': dict(facecolor='white', edgecolor='purple', pad=0.3), 'zorder': 17}))
        all_defect_visualizations.append(vis)
    return all_defect_visualizations


def _get_constrained_detect_region(detect_point: Tuple[float, float, float],
                                   track_dict: Dict,
                                   detect_radius: float,
                                   in_out_wall_relations: Dict[str, List[str]],
                                   out_wall_closed_region: Dict[str, Polygon]) -> Optional[Polygon]:
    """
    Finds the detection area by intersecting a circle with the correct outer wall boundary.

    Args:
        detect_point: The (x, y, z) coordinate of the corner being checked.
        track_dict: The dictionary for the track whose corner is being checked.
        detect_radius: The radius of the detection circle.
        in_out_wall_relations: Mapping of outer wall IDs to the inner wall IDs they contain.
        out_wall_closed_region: Mapping of outer wall IDs to their closed Polygon objects.

    Returns:
        A Shapely Polygon representing the valid detection area, or None if not found.
    """
    def find_parent_outer_wall(child_wall_id: str) -> Optional[str]:
        """Recursively search upwards from a wall to find its containing outer wall."""
        for parent_id, contained_ids in in_out_wall_relations.items():
            if child_wall_id in contained_ids:
                if parent_id.startswith('outer'):
                    return parent_id
                else:
                    return find_parent_outer_wall(parent_id)
        return None

    out_wall_id = find_parent_outer_wall(track_dict['wall_id'])
    if not out_wall_id:
        return None

    # Get the polygon for the outer wall
    constrained_region = out_wall_closed_region.get(out_wall_id)
    if not constrained_region:
        return None

    # Create the detection circle and intersect it with the outer wall region
    detect_circle = Point(detect_point).buffer(detect_radius)
    constrained_detect_region = detect_circle.intersection(constrained_region)

    if constrained_detect_region and not constrained_detect_region.is_empty:
        return constrained_detect_region
    else:
        return None

def _check_point_for_overlap(detect_point: Tuple[float, float, float],
                             track_dict: Dict,
                             all_tracks_dict: List[Dict],
                             rtree_idx: index.Index,
                             in_out_wall_relations: Dict[str, List[str]],
                             out_wall_closed_region: Dict[str, Polygon],
                             detect_radius: float,
                             adjust_ratio: float) -> Tuple[Optional[Dict], set]:
    """
    Checks a single point for a significant overlap defect.

    Returns:
        A tuple containing:
        - A dictionary with defect details if a significant overlap is found, otherwise None.
        - A set of (x, y) tuples for nearby track endpoints that fall within the detection area.
    """
    constrained_detect_region = _get_constrained_detect_region(
        detect_point, track_dict, detect_radius, in_out_wall_relations, out_wall_closed_region
    )
    if not constrained_detect_region:
        return None, set()

    # Find candidate tracks and calculate intersections within the constrained region
    candidate_indices = list(rtree_idx.intersection(constrained_detect_region.bounds))
    polygons_in_region = []
    newly_detected_positions = set()

    for j in candidate_indices:
        candidate_polygon = all_tracks_dict[j]['polygon']
        intersection = constrained_detect_region.intersection(candidate_polygon)
        if intersection and not intersection.is_empty:
            polygons_in_region.append(intersection)

            # Mark endpoints of this nearby track if they fall within the constrained region
            other_track = all_tracks_dict[j]['track']
            if constrained_detect_region.contains(Point(other_track.stPos[:2])):
                newly_detected_positions.add(other_track.stPos[:2])
            if constrained_detect_region.contains(Point(other_track.endPos[:2])):
                newly_detected_positions.add(other_track.endPos[:2])

    if len(polygons_in_region) < 2:
        return None, newly_detected_positions

    # Calculate overlap and material regions to check for significance
    overlap_union, all_overlaps = get_union_of_intersections(polygons_in_region)
    if not overlap_union or overlap_union.is_empty:
        return None, newly_detected_positions

    material_in_region = unary_union(polygons_in_region)
    overlap_area = sum(o.area for o in all_overlaps)
    empty_area = constrained_detect_region.area - material_in_region.area

    if overlap_area > empty_area + constrained_detect_region.area * adjust_ratio:
        defect_info = {'overlap_region': overlap_union, 'detect_point': detect_point,
                       'detect_region': constrained_detect_region, 'material_region': material_in_region,
                       'overlap_volumn': overlap_area * track_dict['track'].height,
                       'g_code_line_num': track_dict['track'].codeLineNum}
        return defect_info, newly_detected_positions

    return None, newly_detected_positions


def detect_inner_wall_overlap(detect_tracks_dict: List[Dict], detect_radius: float, adjust_ratio: float,
                              in_out_wall_relations: Dict[str, List[str]],
                              out_wall_closed_region: Dict[str, Polygon],
                              corner_angle_threshold: float,
                              corner_look_ahead_dist: float) -> List[Dict]:
    """
    Detect every contour corner overlap within a circle area constrained by outer wall boundaries.

    Args:
        detect_tracks_dict: List of dictionaries, each containing a track, its polygon, and wall_id.
        detect_radius: Radius of the circle at the corner to collect adjacent tracks.
        adjust_ratio: A ratio used to judge if an overlap is significant.
        in_out_wall_relations: Mapping of outer wall IDs to the inner wall IDs they contain.
        out_wall_closed_region: Mapping of outer wall IDs to their closed Polygon objects.
        corner_angle_threshold: The angle in degrees. Corners sharper than this will be checked.
        corner_look_ahead_dist: The distance to look forward/backward to calculate corner angles.

    Returns:
        A list of dictionaries, each representing a potential overlap defect.
    """
    overlaps = []
    detected_pos = set()

    # Create a spatial index (R-tree) for fast proximity searches
    rtree_idx = index.Index()
    for i, track_dict in enumerate(detect_tracks_dict):
        polygon = track_dict['polygon']
        rtree_idx.insert(i, polygon.bounds)

    # --- Identify sharp corners of inner walls to use as detection points ---
    from collections import defaultdict
    inner_wall_contours = defaultdict(list)
    for track_dict in detect_tracks_dict:
        if track_dict['track'].lineType == LineType.Inner_wall:
            inner_wall_contours[track_dict['wall_id']].append(track_dict)

    detection_points_info = []
    if inner_wall_contours:
        # Extract just the TrackInfo objects for the filter function
        contours_for_filter = [[td['track'] for td in c] for c in inner_wall_contours.values()]
        
        sharp_corners_2d = []
        for contour in contours_for_filter:
            pts = find_sharp_corners(
                contour,
                angle_threshold_deg=corner_angle_threshold,
                look_ahead_dist=corner_look_ahead_dist
            )
            sharp_corners_2d.extend(pts)
        sharp_corners_set = set(sharp_corners_2d)

        # Map the 2D corner points back to their original 3D points and track_dicts
        for contour_dicts in inner_wall_contours.values():
            for track_dict in contour_dicts:
                track = track_dict['track']
                if track.stPos[:2] in sharp_corners_set:
                    detection_points_info.append({'point': track.stPos, 'track_dict': track_dict})
                if track.endPos[:2] in sharp_corners_set:
                    detection_points_info.append({'point': track.endPos, 'track_dict': track_dict})

    # --- Process each identified sharp corner for overlaps ---
    for info in detection_points_info:
        detect_point = info['point']
        track_dict = info['track_dict']

        if detect_point[:2] in detected_pos:
            continue

        defect, newly_detected = _check_point_for_overlap(
            detect_point, track_dict, detect_tracks_dict, rtree_idx,
            in_out_wall_relations, out_wall_closed_region,
            detect_radius, adjust_ratio
        )

        detected_pos.add(detect_point[:2])
        detected_pos.update(newly_detected)

        if defect:
            overlaps.append(defect)

    return overlaps


def get_union_of_intersections(polygons: List[Polygon]) -> Tuple[Optional[Polygon], List[Polygon]]:
    """
    Get the union of all pairwise intersections between polygons.
    
    Args:
        polygons: List of shapely Polygon objects
        
    Returns:
        Union of all intersection areas, or None if no intersections exist
    """
    intersections = []
    
    # Collect all pairwise intersections
    for i in range(len(polygons)):
        for j in range(i + 1, len(polygons)):
            intersection = polygons[i].intersection(polygons[j])  # intersection is a polygon
            if intersection and not intersection.is_empty:
                intersections.append(intersection)
    
    if not intersections:
        return None, []
    
    # Return union of all intersections
    return unary_union(intersections), intersections
