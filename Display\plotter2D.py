from PySide6.QtWidgets import QVBoxLayout
from matplotlib.figure import Figure
from Display.plotter_base import PlotterBase, color_mode
from Display.visualization_data import DefectVisualization
from GcodeParse.ModelInfo import MotionType, TrackInfo
from matplotlib.collections import LineCollection, PatchCollection
from matplotlib.patches import Circle, Arc, Patch, Polygon as MplPolygon
import matplotlib.pyplot as plt
import numpy as np
from matplotlib.figure import Figure
from shapely.geometry import Polygon, MultiPolygon, GeometryCollection
from matplotlib.backends.backend_qtagg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.backends.backend_qtagg import NavigationToolbar2QT as NavigationToolbar

class plotter2D(PlotterBase):
    def __init__(self, model = None):
        super().__init__(model)
        self.fig = None
        self.ax = None
        self.canvas = None
        self.toolbar = None

    def init_widget(self, parent_widget):
        """Initialize 2D widget in the shared matplotlib_area widget"""
        self.init_widget_layout(parent_widget)

        # Initialize 2D components correctly
        self.fig = Figure()
        self.canvas = FigureCanvas(self.fig)
        self.toolbar = NavigationToolbar(self.canvas, parent_widget)

        parent_widget.layout().addWidget(self.toolbar)
        parent_widget.layout().addWidget(self.canvas)

        self.ax = self.fig.add_subplot(111)
        self.ax.set_xlim(0, 350)
        self.ax.set_ylim(0, 320)
        self.ax.set_aspect("equal")
        self.ax.set_xlabel("x (mm)")
        self.ax.set_ylabel("y (mm)")

    def clear_canvas(self):
        if self.ax is not None:
            for collection in self.ax.collections[:]:
                collection.remove()
            for line in self.ax.lines[:]:
                line.remove()
            for patch in self.ax.patches[:]:
                patch.remove()
            for text in self.ax.texts[:]:
                text.remove()
        self.plt_layer_num = -1
        self.canvas.draw_idle()
    
    def plot_one_layer(self, clr_md=color_mode.line_type_based, alpha=0.99,
                    is_draw_width=False, is_clear_previous=True,
                    track_ratio=100):
        """Plot a single layer with optimized rendering for thousands of tracks."""
        alpha = 0.99 if alpha > 0.99 else alpha
        
        if self.ax is None:
            raise ValueError("plot ax is None")
       
        if is_clear_previous:
            self.clear_canvas()

        # Collect and organize tracks by type with ratio control
        track_data = self._collect_track_data(clr_md, track_ratio)
        
        # Batch plotting mode (for performance)
        self._render_tracks_batch(track_data, is_draw_width, alpha)

        return self.ax
  
    def _collect_track_data(self, clr_md=color_mode.line_type_based, track_ratio=1.0):
        """Collect and organize tracks by type and color with ratio control."""
        track_data = {
            'lines': [],
            'arcs': [],
            'circles': []
        }

        tracks_to_process = self.get_tracks_to_process(track_ratio)
        self.calculate_extreme_values(tracks_to_process, clr_md)
        
        if clr_md == color_mode.layer_based:
            self.plt_layer_num += 1 if self.plt_layer_num < 7 else -7
        
        for track in tracks_to_process:
            if self.should_skip_track(track):
                continue
            
            color = self.get_track_color(track, clr_md)
            
            # Organize by motion type
            if track.motionType == MotionType.G1:
                track_data['lines'].append((track, color))
            elif track.motionType in [MotionType.G2, MotionType.G3]:
                track_data['arcs'].append((track, color))
            elif track.motionType == MotionType.pause:
                track_data['circles'].append((track, color))
        
        return track_data

    def _render_tracks_batch(self, track_data, is_draw_width, alpha) :
        """Render all tracks in batch mode for maximum performance."""
        # Prepare line segments for LineCollection
        line_segments = []
        line_colors = []
        
        for track, color in track_data['lines']:
            line_segments.append([(track.stPos[0], track.stPos[1]), (track.endPos[0], track.endPos[1])])
            line_colors.append(color)
        
        # Create arc patches in batch
        arc_patches = []
        for track, color in track_data['arcs']:
            arc = self.plt_arc_patch(track, color)
            if arc:
                arc_patches.append(arc)
        
        # Create circle patches in batch
        circle_patches = []
        for track, color in track_data['circles']:
            circle = Circle((track.stPos[0], track.stPos[1]), 0.5, color=color, alpha=alpha, zorder=10)
            circle_patches.append(circle)
        
        # Add all patches at once for better performance
        if line_segments:
            line_collection = LineCollection(line_segments, colors=line_colors, linewidths=1, alpha=alpha)
            self.ax.add_collection(line_collection)

        if arc_patches:
            arc_collection = PatchCollection(arc_patches, match_original=True, alpha=alpha)
            self.ax.add_collection(arc_collection)
        
        if circle_patches:
            circle_collection = PatchCollection(circle_patches, match_original=True, alpha=alpha)
            self.ax.add_collection(circle_collection)
        
        # Handle width rendering if needed
        if is_draw_width:
            width_polygons = []
            
            # Line width polygons
            for track, color in track_data['lines']:
                poly = self.plt_line_width_polygon(track, color)
                if poly:
                    width_polygons.append(poly)
            
            # Arc width polygons
            for track, color in track_data['arcs']:
                poly = self.plt_arc_width_polygon(track, color)
                if poly:
                    width_polygons.append(poly)
            
            if width_polygons:
                width_collection = PatchCollection(width_polygons, match_original=True, alpha=0.3, zorder=0)
                self.ax.add_collection(width_collection)

    def get_track_patch(self,track: TrackInfo, is_draw_width, color, alpha):
        """create patch of track according to its MotionType(G1/G2/G3), color, whther draw width"""
        if is_draw_width:
            if track.motionType == MotionType.G1:
                return self.plt_line_width_polygon(track, color, alpha)
            elif track.motionType in [MotionType.G2, MotionType.G3]:
                return self.plt_arc_width_polygon(track, color, alpha)
        else:
            if track.motionType == MotionType.G1:
                return plt.Line2D((track.stPos[0], track.endPos[0]), (track.stPos[1], track.endPos[1]), color=color, alpha=alpha)
            elif track.motionType in [MotionType.G2, MotionType.G3]:
                return self.plt_arc_patch(track, color, alpha)
            elif track.motionType == MotionType.pause:
                return Circle((track.stPos[0], track.stPos[1]), 0.5, color=color, alpha=alpha, zorder=10)
        return None
        
    def plt_arc_patch(self, track: TrackInfo, color, alpha=1.0):
        """Create arc patch from track data"""
        arc_paras = track.get_arc_paras()
        center = arc_paras['center']
        start_angle = np.degrees(arc_paras['start_angle_rad'])
        end_angle = np.degrees(arc_paras['end_angle_rad'])
        radius = arc_paras['radius']      

        return Arc(center, 2 * radius, 2 * radius,
                  theta1=min(start_angle, end_angle),
                  theta2=max(start_angle, end_angle), edgecolor=color,
                  fill=False, facecolor='none', alpha=alpha)

    def plt_line_width_polygon(self, track: TrackInfo, color, alpha=0.3):
        """Create a polygon representing a line with width."""
        pts = track.calc_line_width_paras()
        if pts:
            return plt.Polygon(pts, color=color, alpha=alpha)
        else:
            return None

    def plt_arc_width_polygon(self, track: TrackInfo, color, alpha=0.3):
        """Create a polygon representing an arc with width."""
        arc_paras = track.get_arc_sample_paras()
        center = arc_paras['center']  
        r_outer = arc_paras['r_outer']
        r_inner = arc_paras['r_inner']

        angles = np.array(arc_paras['sample_angles_rad'])
        outer_points = center + r_outer * np.stack([np.cos(angles), np.sin(angles)], axis=1)
        inner_points = center + r_inner * np.stack([np.cos(angles[::-1]), np.sin(angles[::-1])], axis=1)
        polygon_points = np.vstack([outer_points, inner_points])
        
        return plt.Polygon(polygon_points, color=color, alpha=alpha)

    def _draw_polygon(self, geom: any, **style):
        """
        Draw shapely geometry objects (Polygon, MultiPolygon, GeometryCollection).
        """
        if not geom or not hasattr(geom, 'is_valid') or not geom.is_valid:
            return

        # Use a common style dictionary, allowing overrides
        default_style = {'facecolor': 'gray', 'edgecolor': 'gray', 'alpha': 0.3, 'zorder': 10}
        final_style = {**default_style, **style}

        if isinstance(geom, Polygon):
            coords = list(geom.exterior.coords)
            patch = MplPolygon(coords, closed=True, **final_style)
            self.ax.add_patch(patch)

        elif isinstance(geom, (MultiPolygon, GeometryCollection)):
            for sub_geom in geom.geoms:
                self._draw_polygon(sub_geom, **style)

    def _draw_point(self, geom: tuple, **style):
        """Draw a single point using ax.scatter."""
        default_style = {'color': 'red', 's': 20, 'zorder': 10, 'marker': 'o'}
        final_style = {**default_style, **style}
        self.ax.scatter(geom[0], geom[1], **final_style)
    
    def _add_text(self, geom: tuple, text: str, **style):
        """Add a text annotation to the plot."""
        default_style = {'fontsize': 8, 'ha': 'center', 'zorder': 15}
        final_style = {**default_style, **style}
        self.ax.text(geom[0], geom[1], text, **final_style)

    def plot_defects(self, vis_data: list[DefectVisualization]):
        """Universal method to plot all types of defects from a list of DefectVisualization objects."""
        if not vis_data or self.ax is None:
            return

        for vis in vis_data:
            for item in vis.drawables:
                if item.type == 'polygon_2d':
                    self._draw_polygon(item.geometry, **item.style)
                elif item.type == 'track':
                    # Create the patch for the track
                    patch = self.get_track_patch(item.geometry, is_draw_width=False, color=item.style.get('color', 'red'), alpha=item.style.get('alpha', 1.0))
                    # Add the created patch to the axes so it gets drawn
                    if patch:
                        if isinstance(patch, plt.Line2D):
                            self.ax.add_line(patch)
                        else:
                            self.ax.add_patch(patch)
                elif item.type == 'point':
                    self._draw_point(item.geometry, **item.style)
                elif item.type == 'text_annotation':
                    self._add_text(item.geometry, item.text, **item.style)
