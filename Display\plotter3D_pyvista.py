import pyvista as pv
import numpy as np
from GcodeParse.ModelInfo import MotionType, LineType, TrackInfo
import itertools
from Display.plotter_base import PlotterBase, color_mode, LineColor_MAP, color_to_rgb
from Display.visualization_data import DefectVisualization
from pyvistaqt import QtInteractor
from collections import defaultdict

class Plotter3DPyVista(PlotterBase):    
    def __init__(self, model = None):
        super().__init__(model)
        self.plotter = None
        self.qt_widget = None
        self.current_color_mode = None

        # --- State for rendering and updates ---
        self._current_alpha = 1.0
        self._current_line_width = 1.0
        self.batched_tracks = defaultdict(list)

        # --- Geometry and Actor Management ---
        self.blocks = pv.MultiBlock()
        self.block_types = []
        self._actors_by_type = {}

        # --- Defect Geometry and Actor Management ---
        self.defect_actors = []

    # --- Widget Setup and Teardown ---
    def init_widget(self, parent_widget):
        """
        Robustly initialize the PyVista widget within a parent QWidget.
        This method is idempotent and safely handles layout management.
        """
        self.init_widget_layout(parent_widget)

        # Create the PyVista widget, which is both the plotter and the QWidget
        self.plotter = QtInteractor(parent_widget)
        self.qt_widget = self.plotter

        # Add the new widget to the prepared layout
        parent_widget.layout().addWidget(self.qt_widget)
        
        # Setup 3D scene with axes and labels
        self.setup_3d_scene()

    def setup_3d_scene(self):
        """Setup 3D scene with safe settings"""
        try:
            # Basic scene setup without risky modifications
            self.plotter.set_background('white')
            
            # Only add safe global settings
            if hasattr(self.plotter, 'renderer'):
                renderer = self.plotter.renderer
                # Safe renderer modifications only
                try:
                    if hasattr(renderer, 'SetUseLODActor'):
                        renderer.SetUseLODActor(False)
                except Exception as e:
                    print(f"Warning: Could not disable LOD actor: {e}")
                
        except Exception as e:
            print(f"Error in setup_3d_scene: {e}")
            # Continue with minimal setup
            try:
                self.plotter.set_background('white')
            except:
                pass

    def _clear_defects(self):
        """Removes only the defect actors from the plotter and clears defect data."""
        if not self.plotter or not self.defect_actors: return
        for actor in self.defect_actors:
            self.plotter.remove_actor(actor)
        self.defect_actors.clear()

    def clear_canvas(self):
        """Clear the 3D canvas"""
        if self.plotter:
            self.plotter.clear_actors()
            self.blocks.clear()
            self.block_types.clear()
            self._actors_by_type.clear()
            self._clear_defects()
            self.setup_3d_scene()

    def close(self):
        """
        Safely closes the PyVista QtInteractor to release its underlying
        VTK and OpenGL resources. This is crucial to call before the
        application exits to prevent cleanup errors like 'wglMakeCurrent failed'.
        """
        if self.plotter:
            print("Closing PyVista QtInteractor to release OpenGL context.")
            try:
                # The close method of QtInteractor handles the cleanup of the render window
                self.plotter.close()
            except Exception as e:
                print(f"Error while closing the PyVista plotter: {e}")
            finally:
                self.plotter = None
                self.qt_widget = None

    # --- Public Plotting API ---
    def plot_one_layer(self, clr_md=color_mode.line_type_based, alpha=1.0, line_width=1.0, is_clear_previous = True):
        """Clears the scene and renders a single layer of the model."""
        if self.model is None:
            raise ValueError("Model not set")
        if self.plotter is None:
            raise ValueError("3D plotter not initialized")
        if not (0 <= self.layer_id < len(self.model.layers)):
            raise ValueError("Invalid layer_id")
        if is_clear_previous:
            self.clear_canvas()

        tracks = self.model.layers[self.layer_id].tracks
        self._render(tracks, clr_md, alpha, line_width)
        print(f"✅ Rendered layer {self.layer_id}.")

    def plot_model(self, clr_md=color_mode.line_type_based, alpha=1.0, line_width=1.0):
        """Clears the scene and renders the entire 3D model."""
        if self.model is None:
            raise ValueError("Model not set")
        if self.plotter is None:
            raise ValueError("3D plotter not initialized")
        self.clear_canvas()

        tracks_to_render = [t for layer in self.model.layers for t in layer.tracks]
        
        # Performance optimization for very large models
        if len(tracks_to_render) > 250000:
            print(f"Model has {len(tracks_to_render)} tracks, which is large. Plotting only essential walls for performance.")
            types_to_plot = {LineType.Outer_wall, LineType.Inner_wall, LineType.Gap_infill}
            tracks_to_render = [t for t in tracks_to_render if t.lineType in types_to_plot]
            print(f"Rendering {len(tracks_to_render)} essential tracks.")

        self._render(tracks_to_render, clr_md, alpha, line_width)
        print(f"✅ Rendered model.")

    # --- Internal Rendering Pipeline ---
    def _batch_tracks_by_type(self, tracks: list[TrackInfo]) -> dict:
        """Batch tracks by type for efficient rendering"""
        self.batched_tracks = defaultdict(list)
        for track in tracks:
            t_type = track.lineType if track.lineType is not None else track.dotType
            self.batched_tracks[t_type].append(track)
        return self.batched_tracks

    def _render(self, tracks_to_process: list[TrackInfo], clr_md, alpha, line_width):
        """
        Core rendering pipeline that orchestrates the entire process of plotting tracks.
        1. Clears the canvas.
        2. Prepares and batches data.
        3. Builds geometry.
        4. Creates actors and adds them to the scene.
        """
        # Store state for potential updates (e.g., color changes)
        self._current_alpha = alpha
        self._current_line_width = line_width
        self.current_color_mode = clr_md

        # --- 1. Data Preparation ---
        self.ensure_filter_map_initialized()
        self.calculate_extreme_values(tracks_to_process, clr_md)
        self.batched_tracks = self._batch_tracks_by_type(tracks_to_process)

        # --- 2. Geometry Creation ---
        self._build_geometry_blocks(self.batched_tracks, clr_md)

        # --- 3. Actor Creation & Rendering ---
        self._create_actors_from_blocks(alpha, line_width)
        self.plotter.show_axes()
        self.plotter.reset_camera()
        self.plotter.render()

    def _create_actors_from_blocks(self, alpha: float, line_width: float):
        """Iterates through geometry blocks, creates an actor for each, and adds it to the plotter."""
        from GcodeParse.ModelInfo import DotType

        for i, block in enumerate(self.blocks):
            if i >= len(self.block_types): continue  # Safety check
            block_type = self.block_types[i]

            # DotTypes are now meshes (spheres), so they need lighting to look 3D.
            # LineTypes generally don't need lighting.
            use_lighting = True if block_type in DotType else False

            actor = self.plotter.add_mesh(
                block,
                scalars='color',
                rgb=True,
                line_width=line_width,  # Ignored for sphere meshes, used for lines
                opacity=alpha,
                pickable=False,
                lighting=use_lighting  # Enable lighting for spheres to give them a 3D look
            )
            self._actors_by_type[block_type] = actor

            # Set initial visibility based on the filter map
            is_visible = self.line_isPlot_MAP.get(block_type, True)
            actor.SetVisibility(is_visible)

    def _generate_arc_points_3d(self, track: TrackInfo):
        """Generate 3D arc points as a numpy array."""
        track.get_arc_sample_paras()
        angles = track.arc_paras['sample_angles_rad']
        center = track.arc_paras['center'][:2]
        radius = track.arc_paras['radius']

        x = center[0] + radius * np.cos(angles)
        y = center[1] + radius * np.sin(angles)
        z = np.linspace(track.stPos[2], track.endPos[2], len(x))
        
        return np.column_stack((x, y, z))

    def _build_geometry_blocks(self, batched_tracks, clr_md):
        """Add all tracks in the model to the multiblock dataset for rendering."""
        from GcodeParse.ModelInfo import LineType, DotType, MotionType
        if not self.model:
            return 
        
        for type, tracks in batched_tracks.items():
            new_poly = None # Initialize to handle cases where type is not recognized
            pts = []
            lines = []
            colors = []

            # Get lines, point, and color
            if isinstance(type, LineType):
                pts, lines, colors = self._build_track_lines_poly(tracks, clr_md)

                if pts:
                    new_poly = pv.PolyData()
                    new_poly.lines = lines
                    new_poly.points = pts
                    new_poly.point_data['color'] = colors

            elif isinstance(type, DotType):
                for track in tracks:
                    track.num_render_points = 1

                points = np.array([t.stPos for t in tracks], dtype=np.float32)
                c = color_to_rgb(self.get_track_color(tracks[0], clr_md))
                colors = [c] * len(points)
                new_poly = self._build_points_sphere(points, colors)

            if new_poly:
                self.blocks.append(new_poly)
                self.block_types.append(type)
    
    def _build_track_lines_poly(self, tracks: list[TrackInfo], clr_md: color_mode):
        if not tracks:
            return [], [], []

        pts = []
        lines = []
        colors = []
        
        for track in tracks:
            c = color_to_rgb(self.get_track_color(track, clr_md))
            if track.motionType == MotionType.G1:
                p = [track.stPos, track.endPos]
                l = [2, len(pts), len(pts) + 1]
            elif track.motionType in [MotionType.G2, MotionType.G3]:
                #arc is separated into many lines
                p = self._generate_arc_points_3d(track)
                l = [len(p)] + list(range(len(pts), len(pts) + len(p)))
            else:
                continue
            
            # Side-effect: Cache point count on the track object.
            # This is a performance optimization to avoid recalculating for color updates.
            track.num_render_points = len(p)
            
            pts.extend(p)
            lines.extend(l)
            colors.extend([c] * track.num_render_points) 

        return pts, lines, colors

    def _build_points_sphere(self, points: list[tuple], colors: list[tuple], dot_size: float = 0.5):
        # Create a PolyData object for the points
        point_cloud = pv.PolyData(points)
        point_cloud.point_data['color'] = colors

        # Create sphere glyphs at each point. The glyphs will inherit the color.
        # The radius is half the desired diameter (dot_size).
        sphere_geom = pv.Sphere(radius=dot_size / 2.0)
        sphere_glyphs = point_cloud.glyph(geom=sphere_geom, scale=False, orient=False)
        return sphere_glyphs

    def _build_points_glyphs(self, points: list, style: dict) -> pv.PolyData:
        """Builds a PolyData object of sphere glyphs for a list of points, styled according to the provided dictionary."""
        point_cloud = pv.PolyData(points)

        # Use 's' from style for size, map to a radius. This mirrors matplotlib's 's' for scatter size.
        # A simple mapping: radius = sqrt(s) / 20. For s=60, radius is ~0.38.
        marker_size = style.get('s', 40)
        radius = np.sqrt(marker_size) / 20.0 if marker_size > 0 else 0.25

        sphere_geom = pv.Sphere(radius=radius)
        glyphs = point_cloud.glyph(geom=sphere_geom, scale=False, orient=False)
        return glyphs

    # --- Dynamic Updates ---
    def update_visibility(self):
        """Applies the current visibility settings from self.line_isPlot_MAP to all actors."""
        if not self.plotter:
            return
        for line_type, visible in self.line_isPlot_MAP.items():
            if line_type in self._actors_by_type:
                actor = self._actors_by_type[line_type]
                actor.SetVisibility(visible)
        self.plotter.render()

    def update_color(self, clr_md):
        """
        Efficiently updates the colors of existing actors without re-creating them.
        This method modifies the color data on the underlying PolyData meshes.
        """
        if clr_md == self.current_color_mode or not self.plotter:
            return
        
        if not self.batched_tracks:
            print("Warning: Cannot update color. No track data available.")
            return

        print(f"Updating color mode from '{self.current_color_mode}' to '{clr_md}'...")
        self.current_color_mode = clr_md

        from GcodeParse.ModelInfo import LineType, DotType, MotionType

        # We need to find which block corresponds to which type.
        # We can iterate through the original blocks and update their color arrays.
        for i, block in enumerate(self.blocks):
            if i >= len(self.block_types): continue
            
            block_type = self.block_types[i]
            tracks_for_block = self.batched_tracks.get(block_type)

            if not tracks_for_block: continue

            new_colors = []
            # This logic must mirror how colors were created in `add_batched_tracks_to_multiblock`
            for track in tracks_for_block:                
                # Efficiently get the point count from the cached attribute
                if not hasattr(track, 'num_render_points'):
                    print(f"Warning: 'num_render_points' not found on track. Skipping color update for this track.")
                    continue
                
                num_points = track.num_render_points
                c = color_to_rgb(self.get_track_color(track, clr_md))
                new_colors.extend([c] * num_points)

            # Directly update the color data on the mesh, but only if the count is correct.
            if block.n_points == len(new_colors):
                block.point_data['color'] = new_colors
            else:
                print(f"Error: Color array size mismatch for block type {block_type}. "
                      f"Expected {block.n_points}, got {len(new_colors)}. Skipping update for this block.")

        self.plotter.render() # Trigger a re-render to show the new colors.
        print(f"✅ Color mode updated to '{clr_md}'.")

    # --- Add defect plot ---
    def plot_defects(self, vis_data: list[DefectVisualization]):
        """
        Plots defects on top of the existing model by batching geometries with the same style
        for efficient rendering. This method is data-driven and respects the style information
        within each `Drawable`. It clears previous defects before drawing new ones.
        """
        if not self.plotter:
            print("Warning: Plotter not initialized. Cannot plot defects.")
            return

        # 1. Clear any previously plotted defects
        self._clear_defects()

        if not vis_data:
            self.plotter.render()
            return

        # 2. Group all drawable geometries by their style for batch rendering
        drawables_by_style = defaultdict(list)
        for vis in vis_data:
            for item in vis.drawables:
                if item.type not in ['point', 'track']:
                    continue
                # Create a hashable key from the style dictionary to group identical styles
                style_key = frozenset(item.style.items()) if item.style else frozenset()
                batch_key = (item.type, style_key)
                drawables_by_style[batch_key].append(item.geometry)

        # 3. Render each batch of geometries
        for (item_type, style_key), geometries in drawables_by_style.items():
            style = dict(style_key)  # Convert frozenset back to dict for use
            actor = None
            color = style.get('color', 'red')  # Default color if not specified

            if item_type == 'point' and geometries:
                # Batch-render points as sphere glyphs
                glyphs = self._build_points_glyphs(geometries, style)
                actor = self.plotter.add_mesh(
                    glyphs,
                    color=color,
                    lighting=True,
                    pickable=False
                )

            elif item_type == 'track' and geometries:
                # Batch-render tracks by reusing the existing helper function
                # The colors generated by the helper are ignored, as we set a single color for the actor.
                pts, lines, _ = self._build_track_lines_poly(geometries, self.current_color_mode)
                if pts:
                    track_geom = pv.PolyData(pts, lines=lines)
                    actor = self.plotter.add_mesh(
                        track_geom,
                        color=color,
                        line_width=style.get('linewidth', 2.0),
                        lighting=False,
                        pickable=False
                    )
            
            if actor:
                self.defect_actors.append(actor)

        # 4. Handle layer-wide style modifications (e.g., for LayerTimeDiff) separately
        for vis in vis_data:
            if vis.layer_styles:
                for layer_id, style in vis.layer_styles.items():
                    self._render_layer_highlight(layer_id, style)

        self.plotter.render()

    def _render_layer_highlight(self, layer_id: int, style: dict):
        """Helper to find and render all tracks for a specific layer with a given style."""
        # Flatten the lists of tracks from the batched dictionary into a single iterator
        all_tracks_iterator = itertools.chain.from_iterable(self.batched_tracks.values())

        # Now, filter the flattened tracks in a much simpler list comprehension
        layer_highlight_tracks = [
            t for t in all_tracks_iterator
            if t.layer_id == layer_id and not self.should_skip_track(t)
        ]

        if not layer_highlight_tracks:
            return

        pts, lines, _ = self._build_track_lines_poly(layer_highlight_tracks, self.current_color_mode)
        if pts:
            layer_geom = pv.PolyData(pts, lines=lines)
            actor = self.plotter.add_mesh(
                layer_geom,
                color=style.get('color', 'red'),
                line_width=2.0,
                pickable=False
            )
            if actor:
                self.defect_actors.append(actor)