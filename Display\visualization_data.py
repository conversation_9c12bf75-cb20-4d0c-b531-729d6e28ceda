from dataclasses import dataclass, field
from typing import List, Dict, Any, Literal

# Define the types of geometry that can be drawn. This provides type-hinting support.
DrawableType = Literal['point', 'track', 'polygon_2d', 'text_annotation']

@dataclass
class Drawable:
    """
    A generic container for a single piece of geometry and its associated style.
    This is the fundamental building block for all visualizations.
    """
    type: DrawableType
    geometry: Any
    style: Dict[str, Any] = field(default_factory=dict)
    text: str = ""  # Used specifically for 'text_annotation' type

@dataclass
class DefectVisualization:
    """
    A standard container for all visualization data related to a single defect
    instance or a set of related defects (e.g., a continuous overhang).
    """
    # A list of all drawable components for this defect.
    drawables: List[Drawable] = field(default_factory=list)
    # Instructions to modify existing geometry (e.g., coloring entire layers).
    layer_styles: Dict[int, Dict[str, Any]] = field(default_factory=dict)
    # Optional metadata for display in UI text boxes or for filtering.
    metadata: Dict[str, Any] = field(default_factory=dict)

