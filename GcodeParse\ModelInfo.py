from enum import Enum
from typing import List, Optional
from rtree import index
from shapely.geometry import MultiPolygon, Polygon, Point
import math
import datetime
import numpy as np
from shapely.ops import unary_union

from Defect.DefectType import DefectType

class LineType(Enum):
    Empty_move = "Empty move"
    Wipe="Wipe"
    Gap_infill = "Gap infill"
    Floating_vertical_shell="Floating vertical shell"
    Internal_solid_infill="Internal solid infill"
    Sparse_infill="Sparse infill"
    Outer_wall="Outer wall"
    Inner_wall="Inner wall"
    Overhang_wall="Overhang wall"
    Bottom_surface="Bottom surface"
    Top_surface = "Top surface"
    Bridge="Bridge"
    Support="Support"
    Support_interface = "Support interface"
    Support_transition="Support transition"
    Brim="Brim"
    Custom="Custom"

class DotType(Enum):
    Withdraw = "Withdraw"
    Seam = "Seam"
    AdjustPara = "AdjustPara"
    Reload = "Reload"

class MotionType(Enum):
    G1 = "G1"
    G2 = "G2"
    G3 = "G3"
    pause = "Pause"

class TrackInfo:
    def __init__(self, st_pos, end_pos = 0, c_pos = None, ex = 0, speed = 0):
        # Geometry
        self.stPos: tuple[float, float, float] = st_pos
        self.endPos: tuple[float, float, float] = end_pos
        self.cicPos: tuple[float, float, float] = c_pos
        self.length = 0
        self.width_comment = 0
        self.width = 0
        self.height = 0
        self.polygon: Polygon = None

        # G-code info
        self.ex = ex
        self.speed = speed
        self.accel = 0
        self.time = None
        self.codeLineNum = 0
        self.layer_id = None
        self.lineType: LineType = None
        self.dotType: DotType = None
        self.motionType: MotionType = None

        # Defect info
        self.defectType: DefectType = None
        
        # G2/3 discrete parameters
        self.chord_error_mm = 0.01
        self.max_discrete_num = 50

    def calc_track_length(self):
        if self.motionType == MotionType.G1:
            self.length = calculate_line_length(self.stPos, self.endPos)
            
        elif self.motionType in [MotionType.G2, MotionType.G3]:
            self.get_arc_paras()
            angle_diff_rad = abs(self.arc_paras['start_angle_rad'] - self.arc_paras['end_angle_rad'])
            self.length = self.arc_paras['radius'] * abs(angle_diff_rad)
            
        elif self.motionType == MotionType.pause:
            self.length = 0

        else:
            raise TypeError("not supported MotionType")
    
    def get_arc_paras(self):
        """
        Calculate some common used parameters of an arc track, converting an arc track into separate short lines.
        """
        if hasattr(self, 'arc_paras'):
            return self.arc_paras

        if not self.motionType in [MotionType.G2, MotionType.G3]:
            raise TypeError("not an arc track")
        
        start = np.array(self.stPos[:2])
        end = np.array(self.endPos[:2])
        center = np.array(self.cicPos[:2])
        
        # Radius
        radius = np.linalg.norm(start - center)
        if radius  == 0:
            raise ValueError("Invalid radius: R == 0")

        # Calculate angles in radians
        vec_start = start - center
        vec_end = end - center
        start_angle =np.arctan2(vec_start[1], vec_start[0])
        end_angle = np.arctan2(vec_end[1], vec_end[0])
        
        # Revise angle by direction
        clockwise = self.motionType == MotionType.G2
        if clockwise and end_angle >= start_angle:
            end_angle -= 2*math.pi
        elif not clockwise and end_angle <= start_angle:
            end_angle += 2*math.pi       
                
        self.arc_paras = {
            'center': center,
            'radius': radius,
            'start_angle_rad': start_angle,
            'end_angle_rad': end_angle,
            'clockwise': clockwise
        }
        return self.arc_paras

    def get_arc_sample_paras(self, use_equivalent: bool = False):
        """
        Sample points along the arc track.
        
        Args:
            chord_error_mm: the maximum allowed error between the arc and the poly-line in mm.
            max_discrete_num: the maximum number of discrete points allowed.
        """
        if  hasattr(self, 'arc_paras') and 'sample_angles_rad' in self.arc_paras.keys():
            return self.arc_paras

        arc_paras = self.get_arc_paras()

        start_angle = arc_paras['start_angle_rad']
        end_angle = arc_paras['end_angle_rad']
        radius = arc_paras['radius']
        center = arc_paras['center']
        start = np.array(self.stPos[:2])
        end = np.array(self.endPos[:2])

        # Radius for polygon of line width
        if self.width > 0:
            if use_equivalent:
                r_outer = radius + self.get_equivalent_rectangle_width() / 2.0
                r_inner = max(0, radius - self.get_equivalent_rectangle_width() / 2.0)
            else:
                r_outer = radius + self.width / 2.0
                r_inner = max(0, radius - self.width / 2.0)
        else:
            r_outer = r_inner = radius

        # Sample arc at a certain chord_error
        n_pts = self._calc_arc_discrete_num(self.chord_error_mm, self.max_discrete_num)
        if n_pts <= 2:
            sample_angles = [start_angle, end_angle]
            sample_pts = [start, end]
        else:
            sample_angles = np.linspace(start_angle, end_angle, n_pts)
            sample_pts = center + radius * np.column_stack((np.cos(sample_angles), np.sin(sample_angles)))        
                
        self.arc_paras.update({
            'r_outer': r_outer,
            'r_inner': r_inner,
            'sample_angles_rad': sample_angles,
            'sample_points': sample_pts
        })
        return self.arc_paras

    def _calc_arc_discrete_num(self, chord_error_mm, max_discrete_num):
        """
        Calculate the number of discrete points to sample an arc track, based on the chord error.
        """
        arc_paras = self.get_arc_paras()
        radius = arc_paras['radius']
 
        # Handle edge cases:
        if radius <= 1e-9 or chord_error_mm <= 0:
            return max_discrete_num
 
        t = chord_error_mm / radius
        if t >= 1:
            return 2  # Start and end points
        
        alpha = np.arccos(1 - t)
        if alpha < 1e-9:
            return max_discrete_num
 
        delta_angle = abs(arc_paras['end_angle_rad'] - arc_paras['start_angle_rad'])
        num_segments = math.ceil(delta_angle / (2 * alpha))
        n_pts = int(num_segments) + 1
        return min(max_discrete_num, n_pts)

    def calc_line_width_paras(self, use_equivalent: bool = False) -> List[tuple[float, float]]:
        """
        Calculate the vertex coordinates of track polygon (if linear) with a certain width.
        Args:
            use_equivalent: whether to use equivalent rectangle width
        Return:
            currently 4 points representing a rectangle
        """
        if self.width > 0:
            dx = self.endPos[0] - self.stPos[0]
            dy = self.endPos[1] - self.stPos[1]
            length = np.hypot(dx, dy) # universal for G1/G2/G3           
                
            ux, uy = dx / length, dy / length
            nx, ny = -uy, ux  # Normal vector
            if use_equivalent:    
                half_w = self.get_equivalent_rectangle_width() / 2.0
            else:
                half_w = self.width / 2.0
            
            p1 = (self.stPos[0] + nx * half_w, self.stPos[1] + ny * half_w)
            p2 = (self.stPos[0] - nx * half_w, self.stPos[1] - ny * half_w)
            p3 = (self.endPos[0] - nx * half_w, self.endPos[1] - ny * half_w)
            p4 = (self.endPos[0] + nx * half_w, self.endPos[1] + ny * half_w)
            
            return[p1, p2, p3, p4]
        else:
            return None
 
    def build_track_width_polygon(self, use_equivalent: bool = False) -> Optional[Polygon]:
        """
        Build polygon of track with a certain width
        """
        if not hasattr(self, 'width') or self.width < 0:
            raise ValueError("Invalid width")
        elif self.width == 0:
            return None


        if self.motionType in [MotionType.G2, MotionType.G3]:
            poly = self._build_arc_width_polygon(use_equivalent)

        # Build a rectangular polygon representing a line segment with width.
        elif self.motionType == MotionType.G1:
            pts = self.calc_line_width_paras(use_equivalent)
            poly = Polygon(pts) if pts else None
        else:
            return None
        
        if not poly.is_valid or poly.is_empty:
            raise ValueError("Invalid polygon")

        return poly

    def _build_arc_width_polygon(self, use_equivalent: bool = False) -> Optional[Polygon]:
        """
        Build polygon for arc segment with accurate geometry using dual arc approach.

        Args:
            angle_step_deg: Angular step for sampling precision
            min_angle_deg: Minimum angle to consider as arc (otherwise treat as line)
            min_dist: Minimum distance to consider as arc

        Returns:
            Shapely Polygon representing the arc with width, or None if invalid
        """
        arc_paras = self.get_arc_sample_paras(use_equivalent=use_equivalent)
        r_outer = arc_paras['r_outer']
        r_inner = arc_paras['r_inner']
        cx, cy = arc_paras['center']
        angles_outer = arc_paras['sample_angles_rad']
        angles_inner = angles_outer[::-1]  # Inner ring in reverse for closure

        outer_pts = [(cx + r_outer * np.cos(a), cy + r_outer * np.sin(a)) for a in angles_outer]
        inner_pts = [(cx + r_inner * np.cos(a), cy + r_inner * np.sin(a)) for a in angles_inner]

        ring = outer_pts + inner_pts
        if len(ring) < 3:
            return None
        else: 
            poly = Polygon(ring)
            return poly if poly.is_valid else None
 
    def calc_and_set_width(self, filament_flow_ratio = 1.0):
        """Calculate the width of the track based on the flow ratio"""
        calib_ex = self.ex / filament_flow_ratio if filament_flow_ratio is not None else self.ex
        if self.motionType in [MotionType.G1, MotionType.G2, MotionType.G3] and self.ex > 0:                   
            self.width = (calib_ex * math.pi * 0.875 **2 / self.length / self.height) - math.pi * self.height / 4 + self.height                                 
            if (abs(self.width_comment - self.width) / self.width_comment > 0.1):
                # print(f"Much error in calculating width: {self.width} vs {self.width_comment} in line {line_num}")
                self.width = self.width_comment
                # if self.ex < 5e-4 or self.lineType == LineType.Bridge: # error is large for 1. ex is quite samll; 2. lineType is Bridge
                #     pass 
                # else:
                #     pass # maybe Cross-layer printing
        else:
            self.width = 0
        return self.width
        
    def get_equivalent_rectangle_width(self) -> float:
        """Get the width of equivalent rectangle with a eaqual corss section"""
        return self.width - 0.2146 * self.height # V = [self.width - (1 - math.pi/4) * self.height] * self.height
    

class LayerInfo:
    def __init__ (self,z=0,h=0):
        self.z=z
        self.height=h
        self.time=0
        self.tracks: List[TrackInfo]=[]
        self.defectType: DefectType = None
        
    def extract_outer_inner_wall_contour(self):
        wall_countour=[]
        self.outer_wall_countour_list=[]
        self.inner_wall_countour_list=[]
        self.overhang_wall_countour_list=[]
        is_extracting_outer=False
        is_extracting_inner=False
        is_extracting_overhang=False
        if self.tracks:
            for track in self.tracks:
                if not is_extracting_outer and not is_extracting_inner and not is_extracting_overhang:
                    if track.lineType == LineType.Outer_wall:
                        is_extracting_outer=True
                    elif track.lineType == LineType.Inner_wall:
                        is_extracting_inner=True
                    elif track.lineType == LineType.Overhang_wall:
                        is_extracting_overhang=True
                # if the countour has inner_wall or outer_wall, it is not totally overhang
                if  track.lineType in [LineType.Inner_wall,LineType.Outer_wall, LineType.Overhang_wall]:
                    wall_countour.append(track)
                    if track.lineType == LineType.Outer_wall:
                        is_extracting_outer=True
                        is_extracting_overhang=False
                    elif track.lineType == LineType.Inner_wall:
                        is_extracting_inner=True
                        is_extracting_overhang=False
                if track.lineType in [LineType.Empty_move, LineType.Wipe] or track == self.tracks[-1]: # end of a contour
                    if is_extracting_outer:
                        self.outer_wall_countour_list.append(wall_countour)
                    elif is_extracting_inner:
                        self.inner_wall_countour_list.append(wall_countour)
                    elif is_extracting_overhang:
                        self.overhang_wall_countour_list.append(wall_countour)# totally overhang
                    is_extracting_outer=False
                    is_extracting_inner=False
                    is_extracting_overhang=False
                    wall_countour=[]
                    
    def extract_z_seam(self):
        def find_countour_closed_point(wall_countour_list: list, z_seam_store: list):
            for wall_countour in wall_countour_list:
                if calculate_line_length(wall_countour[0].stPos, wall_countour[-1].endPos) < 0.2: 
                   seam_pos = wall_countour[-1].endPos
                   z_seam_store.append(seam_pos)

                   last_track_idx = self.tracks.index(wall_countour[-1])
                   seam_track = TrackInfo(seam_pos, seam_pos)
                   seam_track.codeLineNum = wall_countour[-1].codeLineNum
                   seam_track.dotType = DotType.Seam
                   seam_track.motionType = MotionType.pause
                   seam_track.layer_id = self.tracks[-1].layer_id
                   self.tracks.insert(last_track_idx + 1, seam_track)

        self.z_seam_outer=[]
        self.z_seam_inner=[]
        self.z_seam_overhang=[]
        if not self.outer_wall_countour_list or not self.inner_wall_countour_list or not self.overhang_wall_countour_list:
            self.extract_outer_inner_wall_contour()
        if self.outer_wall_countour_list:
            find_countour_closed_point(self.outer_wall_countour_list, self.z_seam_outer)
        if self.inner_wall_countour_list:
            find_countour_closed_point(self.inner_wall_countour_list, self.z_seam_inner)
        if self.overhang_wall_countour_list:
            find_countour_closed_point(self.overhang_wall_countour_list,self.z_seam_overhang)

    def extract_one_lineType(self, lineType: LineType):
        lineType_countour_list=[]
        if self.tracks:
            for track in self.tracks:
                if track.lineType == lineType:
                    lineType_countour_list.append(track)
        else:
            raise TypeError("self.tracks is NoneType")
        return lineType_countour_list
    
    def extract_one_dotType(self, dotType: DotType):
        dot_list=[]
        if self.tracks:
            for track in self.tracks:
                if track.dotType == dotType:
                    dot_list.append(track)
        else:
            raise TypeError("self.tracks is NoneType")
        return dot_list

    def build_wall_relations(self) -> dict:
        """Build polygons for all wall contours and determine inclusion relationships."""
        if hasattr(self, 'wall_and_inclusions'):
            return self.wall_and_inclusions
        
        if not hasattr(self, 'outer_wall_countour_list') or not hasattr(self, 'inner_wall_countour_list'):
            self.extract_outer_inner_wall_contour()
        
        result = {
            'outer_polygons': [],
            'inner_polygons': [],
            'overhang_polygons': [],
            'in_out_inclusion_map': {},
            'outer_inclusion_map': {}
        }
        
        def build_wall_closed_polygons_list(wall_countour_list: list[list[TrackInfo]], id_prefix: str):
            """form closed polygon from wall contour"""
            polygon_dict_list = []
            if wall_countour_list:
                for i, contour in enumerate(wall_countour_list):
                    polygon_dict_list.append({
                            'polygon': build_contour_closed_polygon(contour),
                            'contour': contour,
                            'id': f'{id_prefix}{i}'
                        })
            return polygon_dict_list if len(polygon_dict_list) > 0 else []
                
        result['outer_polygons'] = build_wall_closed_polygons_list(self.outer_wall_countour_list, 'outer_')
        result['inner_polygons'] = build_wall_closed_polygons_list(self.inner_wall_countour_list, 'inner_')
        result['overhang_polygons'] = build_wall_closed_polygons_list(self.overhang_wall_countour_list, 'overhang_')

        # Find inclusion relationship of walls
        result['in_out_inclusion_map'] = self._get_wall_inclusion_relationships(result['outer_polygons'] + result['inner_polygons'] + result['overhang_polygons'])
        result['outer_inclusion_map'] = self._get_outer_wall_inclusion(result['in_out_inclusion_map'])

        self.wall_and_inclusions = result
        return self.wall_and_inclusions

    def _get_wall_inclusion_relationships(self, wall_polygons: List[dict]) -> dict:   
        """
        Determine wall inclusions using an R-tree for efficient spatial queries.
        """
        if not wall_polygons:
            return {}

        # 1. Build the R-tree index from all polygon bounding boxes.
        idx = index.Index()
        for i, poly_dict in enumerate(wall_polygons):
            # Store the index `i` in the R-tree, keyed by the polygon's bounding box.
            if poly_dict['polygon']:
                idx.insert(i, poly_dict['polygon'].bounds)

        # 2. Initialize the inclusion map.
        inclusion_map = {p['id']: [] for p in wall_polygons}

        # 3. For each polygon, find its immediate parent.
        for i, poly_dict in enumerate(wall_polygons):
            current_id = poly_dict['id']
            current_polygon = poly_dict['polygon']
            if not current_polygon:
                continue
            
            # Use a representative point from within the polygon.
            test_point = Point(poly_dict['contour'][0].stPos[:2])

            # Query the R-tree to find all polygons whose bounding boxes contain the test point.
            candidate_indices = list(idx.intersection(test_point.bounds))

            # Find the smallest valid container from the candidates.
            smallest_container_id = None
            smallest_container_area = float('inf')

            for j in candidate_indices:
                if i == j: continue # Skip self-comparison
                candidate_dict = wall_polygons[j]
                # The candidate must be larger and must actually contain the test point.
                if candidate_dict['polygon'].area > current_polygon.area and candidate_dict['polygon'].contains(test_point):
                    if candidate_dict['polygon'].area < smallest_container_area:
                        smallest_container_area = candidate_dict['polygon'].area
                        smallest_container_id = candidate_dict['id']
            
            if smallest_container_id:
                inclusion_map[smallest_container_id].append(current_id)

        return inclusion_map

    def _get_outer_wall_inclusion(self, in_out_inclusion_map: dict) -> dict:
        """Determine which outer polygons are contained within other outer polygons."""
        # Create a deep copy to avoid modifying the original
        copy_io_map = {k: v.copy() for k, v in in_out_inclusion_map.items() if k.startswith('outer')}
        
        # Process each outer wall
        for key in list(copy_io_map.keys()):
            if not key.startswith('outer'):
                copy_io_map.pop(key)
                continue
                
            # Process each contained item
            i = 0
            while i < len(copy_io_map[key]):
                value = copy_io_map[key][i]
          
                # If it's an inner wall, replace it with its contained outer walls
                if value.startswith('inner') and value in in_out_inclusion_map:
                    outer_walls = [v for v in in_out_inclusion_map[value]]
                    copy_io_map[key].remove(value)
                    copy_io_map[key].extend(outer_walls)
                    # Don't increment i since we removed an element
                    continue
                i += 1
        return copy_io_map

    def get_outer_wall_inclusion_in_link(self):
        """
        Generate a link list and a flatten list whose context is consistent with the incusion relationship in self.wall_and_inclusions['outer_inclusion_map'].
        If included wall has inclusion, it should further expand.

        Return:
            A link list of some chains indicating inclusion relationship.
            A flatten list containing wall_id and is_hole information.
        """
        if not hasattr(self, "wall_and_inclusions"):
            self.build_wall_relations()
        
        out_map = self.wall_and_inclusions['outer_inclusion_map']
        
        # Initialize the linked list structure
        linked_list = {}
        flatten_list = []
        
        # Find top-level outer walls (those not contained by any other outer wall)
        top_level_walls = []
        all_contained_walls = set()
        for contained_list in out_map.values():
            all_contained_walls.update(contained_list)
            
        for outer_id in out_map.keys():
            if outer_id not in all_contained_walls:
                top_level_walls.append(outer_id)
        
        # Recursively build the linked list
        def build_node(wall_id, is_hole):
            node = {'id': wall_id, 'children': [], 'is_hole': is_hole}
            flatten_list.append((wall_id, is_hole))
            if wall_id in out_map:
                for child_id in out_map[wall_id]:
                    node['children'].append(build_node(child_id, not is_hole))
            return node

        for wall_id in top_level_walls:   
            linked_list[wall_id] = build_node(wall_id, False)

        # Update inclusion level in     
        return linked_list, flatten_list

    def get_outer_wall_closed_region(self):
        """
        Two adjacent outer walls formed a closed region (shrink by half of the wall width). 
        This function calculate all the closed regions in this layer.
        """
        # Check property
        if hasattr(self, 'outer_wall_closed_region'):
            return self.outer_wall_closed_region

        if not hasattr(self, 'wall_and_inclusions'):
            self.build_wall_relations() 

        if not self.wall_and_inclusions:
            return None

        _, out_wall_inclusion_relation_flatten_list = self.get_outer_wall_inclusion_in_link()
        out_wall_closed_polygons = [poly['polygon'] for poly in self.wall_and_inclusions['outer_polygons']] 
        out_wall_fisrt_tracks = [track['contour'][0] for track in self.wall_and_inclusions['outer_polygons']]

        self.outer_wall_closed_region ={}

        # for a outwall containing list from largest to smallest, they are [wall, hole, wall, hole, ...]
        inner_polys = []
        current_support_island = None
        out_wall_id = 0     
        for wall_id, is_hole in out_wall_inclusion_relation_flatten_list:
            wall_index = int(wall_id.split('_')[1])
            poly = out_wall_closed_polygons[wall_index]
            if not poly:
                continue
            width_offset = out_wall_fisrt_tracks[wall_index].get_equivalent_rectangle_width() / 2  
            if not is_hole:   
                if current_support_island:
                    if inner_polys:
                        # Substrate all hole polygons once for more efficient
                        current_support_island = current_support_island.difference(unary_union(inner_polys))
                    if current_support_island and not current_support_island.is_empty:
                        self.outer_wall_closed_region[out_wall_id] = current_support_island     
                current_support_island = poly.buffer(- width_offset)
                inner_polys = []
                out_wall_id = wall_id
            else:
                inner_polys.append(poly.buffer(width_offset))

        # Add the last processed island to the list.
        if current_support_island:
            if inner_polys:
                current_support_island = current_support_island.difference(unary_union(inner_polys))
            if current_support_island and not current_support_island.is_empty:
                self.outer_wall_closed_region[out_wall_id] = current_support_island     
        return self.outer_wall_closed_region         

    def check_is_support_layer(self):
        # if hasattr(self, 'is_support_layer'):
        #     return self.is_support_layer
        
        self.is_support_layer = True
        for track in self.tracks:
            if track.ex > 0 and track.lineType and track.lineType not in [LineType.Support, LineType.Support_interface, LineType.Support_transition]:
                self.is_support_layer = False
                break
        return self.is_support_layer

class ModelInfo:
    def __init__ (self):
        self.layers: List[LayerInfo]=[]
        self.paras={}
        self.defectType = None

    def get_one_para(self, para_name: str, id : int = None):
        """Get one parameter from the model"""

        if para_name in self.paras.keys():
            if type(self.paras[para_name]) is list and 'filament' in self.paras.keys():
                filament_id = int(self.paras['filament']) - 1 if id is None else id
                if filament_id < 0 or filament_id >= len(self.paras[para_name]):
                    raise ValueError(f"Invalid filament id: {filament_id}")
                return self.paras[para_name][filament_id]
            else:
                return  self.paras[para_name]

    def store_discrete_data(self):
        """calculate all G2/G3 tracks in model to discrete lines"""
        for layer in self.layers:
            for track in layer.tracks:
                if track.motionType in [MotionType.G2, MotionType.G3]:
                    track.get_arc_sample_paras()

# Utility 
def calculate_arc_length(start: tuple[float, float, float], end: tuple[float, float, float], center: tuple[float, float, float], is_clockwise: bool):
    """
    计算圆弧长度（改进版）
    :param start: 起点坐标 [x, y]
    :param end: 终点坐标 [x, y]
    :param center: 圆心坐标 [x, y]
    :param is_clockwise: 是否为顺时针方向
    :return: 弧长
    """
    # 计算起点和终点的角度（弧度制，范围 [-π, π]）
    start_angle = np.arctan2(start[1] - center[1], start[0] - center[0])
    end_angle = np.arctan2(end[1] - center[1], end[0] - center[0])

    # 统一转换为 [0, 2π] 范围
    start_angle = start_angle + 2 * math.pi if start_angle < 0 else start_angle
    end_angle = end_angle + 2 * math.pi if end_angle < 0 else end_angle

    # 计算原始角度差
    angle_diff = end_angle - start_angle

    # 根据方向修正角度差
    if is_clockwise:
        angle_diff = angle_diff - 2 * math.pi if angle_diff > 0 else angle_diff
    else:
        angle_diff = angle_diff + 2 * math.pi if angle_diff < 0 else angle_diff

    # 计算半径和弧长
    radius = np.linalg.norm(np.array(start) - np.array(center))
    arc_length = radius * abs(angle_diff)

    return arc_length

def calculate_line_length(stPos: tuple[float, float, float], endPos: tuple[float, float, float]):
    return np.linalg.norm(np.array(stPos) - np.array(endPos))

def build_contour_closed_polygon(contour: List[TrackInfo]) -> Polygon:
    """Build a closed polygon from a contour of tracks."""
    if not contour:
        return None
    
    #check if closed by endpoints' distance
    if calculate_line_length(contour[0].stPos, contour[-1].endPos) > 0.2:
        # print("contour is not closed when calling func: build_contour_closed_polygon()")
        return None
    
    points = []
    for track in contour:
        if track.motionType == MotionType.G1:
            # For lines, just add start point (end will be next track's start)
            points.append(track.stPos[:2])
        elif track.motionType in [MotionType.G2, MotionType.G3]:
            # For arcs, sample points along the curve
            arc_points = track.get_arc_sample_paras()['sample_points']
            points.extend(arc_points[:-1])  # Exclude last point to avoid duplication
    
    # Add the last track's end point to close the contour
    points.append(contour[-1].endPos[:2])
    
    if len(points) < 3:
        return None
    
    # Check if polygon is valid
    polygon = Polygon(points)
    if polygon.is_valid and not polygon.is_empty:
        return polygon
    else:
        # Try to repair the polygon using the buffer(0) trick
        repaired_polygon = polygon.buffer(0)
        if repaired_polygon.is_valid and not repaired_polygon.is_empty:
            return repaired_polygon

        # If still invalid, then export for debugging
        print("Unrepairable contour to build polygon. Exporting points for debugging.")
        # Generate a unique filename
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S_%f")
        filename = f"invalid_polygon_points_{timestamp}.txt"

        # Write points to the file
        try:
            with open(filename, 'w') as f:
                f.write("# Points for invalid polygon\n")
                for point in points:
                    f.write(f"{point[0]},{point[1]}\n")
            print(f"Points saved to {filename}")
        except IOError as e:
            print(f"Error writing to file {filename}: {e}")
        return None
