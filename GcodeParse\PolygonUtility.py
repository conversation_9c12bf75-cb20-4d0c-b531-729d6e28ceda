import numpy as np
from typing import List, Tu<PERSON>, Optional
import matplotlib.pyplot as plt
from shapely.geometry import <PERSON>S<PERSON>, Polygon, MultiPolygon, CAP_STYLE, JOIN_STYLE


def _fast_mitered_offset(line_points: np.ndarray, half_widths: np.ndarray, is_closed: bool) -> Optional[np.ndarray]:
    """
    Calculates the vertices of a polygon by offsetting a polyline with mitered joins.
    This is a fast, NumPy-based alternative to shapely's buffer, but it does not handle self-intersections.
    """
    if len(line_points) < 2:
        return None

    # 1. Calculate tangent and normal vectors for each segment
    segments = np.diff(line_points, axis=0)
    lengths = np.linalg.norm(segments, axis=1)

    # Filter out zero-length segments to avoid division by zero
    valid_segments = lengths > 1e-9
    if not np.any(valid_segments): return None

    # Normalize tangents
    tangents = np.zeros_like(segments)
    tangents[valid_segments] = segments[valid_segments] / lengths[valid_segments, np.newaxis]

    # Normals are tangents rotated by 90 degrees
    normals = np.column_stack([-tangents[:, 1], tangents[:, 0]])

    # 2. Calculate offset points
    left_pts = []
    right_pts = []

    # --- Start Cap (for open lines) ---
    if not is_closed:
        offset_vec = normals[0] * half_widths[0]
        left_pts.append(line_points[0] + offset_vec)
        right_pts.append(line_points[0] - offset_vec)

    # --- Mitered Joins (for interior vertices) ---
    num_joins = len(line_points) - 1 if is_closed else len(line_points) - 2

    for i in range(num_joins):
        p = line_points[i + 1]
        w = half_widths[i + 1]

        n_in = normals[i]
        n_out = normals[(i + 1) % len(normals)]

        miter_vec = n_in + n_out
        miter_norm = np.linalg.norm(miter_vec)

        if miter_norm < 1e-9:  # Segments are collinear and opposite
            miter_vec = n_in  # Fallback to the incoming normal
            miter_len_factor = 1.0
        else:
            miter_vec /= miter_norm
            # Calculate the miter length adjustment factor to maintain constant width
            cos_val = np.dot(miter_vec, n_in)
            if cos_val < 1e-9:  # Angle is too sharp, cap the miter to prevent extreme spikes
                miter_len_factor = 5.0
            else:
                miter_len_factor = 1.0 / cos_val

        offset_dist = w * miter_len_factor
        left_pts.append(p + miter_vec * offset_dist)
        right_pts.append(p - miter_vec * offset_dist)

    # --- End Cap (for open lines) ---
    if not is_closed:
        offset_vec = normals[-1] * half_widths[-1]
        left_pts.append(line_points[-1] + offset_vec)
        right_pts.append(line_points[-1] - offset_vec)
    else:
        # For a closed loop, close the polygon by connecting back to the start
        left_pts.append(left_pts[0])
        right_pts.append(right_pts[0])

    # 3. Assemble the final polygon vertices
    if not left_pts or not right_pts:
        return None

    # The right points need to be in reverse order to form a continuous loop
    return np.array(left_pts + right_pts[::-1])


def continious_line_with_variable_width(points: List[Tuple[float, float]], widths: List[float], closure_tolerance: float = 0.1) -> Optional[List[Polygon]]:
    """
    Creates a polygon from a polyline with a variable width.
    This function uses a fast NumPy-based method and falls back to shapely's robust
    buffer only when the fast method produces an invalid (self-intersecting) polygon.

    Args:
        points: A list of (x, y) tuples for the line's vertices.
        widths: A list of width values, one for each point in the line.
        closure_tolerance: Distance to consider the polyline closed.

    Returns:
        A list of valid shapely Polygon objects, or None if input is invalid.
    """
    if len(points) < 2 or len(points) != len(widths):
        return None

    line_points = np.array(points, dtype=float)
    half_widths = np.array(widths, dtype=float) / 2.0

    # --- 1. Check if the polyline is a closed loop ---
    is_closed = False
    # A line needs at least 3 points to be considered for closure.
    if len(points) > 2 and np.linalg.norm(line_points[0] - line_points[-1]) < closure_tolerance:
        is_closed = True
        # For calculations, explicitly close the loop
        line_points = np.vstack([line_points, line_points[0]])
        half_widths = np.append(half_widths, half_widths[0])

    # --- 2. Fast polygon generation using NumPy ---
    polygon_vertices = _fast_mitered_offset(line_points, half_widths, is_closed)

    if polygon_vertices is None or len(polygon_vertices) < 3:
        # Fallback to robust buffer if fast method fails to produce vertices
        return _robust_buffer_fallback(line_points, half_widths, is_closed)

    # --- 3. Validate and Fix ---
    try:
        fast_polygon = Polygon(polygon_vertices)
        if fast_polygon.is_valid:
            # Fast method succeeded and polygon is valid
            buffered_result = fast_polygon
        else:
            # Fast method produced a self-intersecting polygon, fix it with buffer(0)
            buffered_result = fast_polygon.buffer(0)
    except Exception:
        # If Polygon creation fails for any reason, fallback to robust buffer
        return _robust_buffer_fallback(line_points, half_widths, is_closed)

    # --- 4. Unify result to a list of Polygons ---
    if not buffered_result or buffered_result.is_empty:
        return None

    polygons = []
    if isinstance(buffered_result, Polygon):
        polygons.append(buffered_result)
    elif isinstance(buffered_result, MultiPolygon):
        polygons.extend([p for p in buffered_result.geoms if not p.is_empty])

    return polygons if polygons else None


def _robust_buffer_fallback(line_points: np.ndarray, half_widths: np.ndarray, is_closed: bool) -> Optional[List[Polygon]]:
    """The original robust but slow shapely buffer method."""
    line_string = LineString(line_points)
    buffered_result = line_string.buffer(
        distance=half_widths,
        join_style=JOIN_STYLE.round,
        cap_style=CAP_STYLE.round if not is_closed else CAP_STYLE.flat
    )

    if not buffered_result or buffered_result.is_empty:
        return None

    polygons = []
    if isinstance(buffered_result, Polygon):
        polygons.append(buffered_result)
    elif isinstance(buffered_result, MultiPolygon):
        # Decompose MultiPolygon into a list of Polygons
        polygons.extend([p for p in buffered_result.geoms if not p.is_empty])
    elif isinstance(buffered_result, np.ndarray):
        # Filter the numpy array for valid Polygons
        polygons.extend([p for p in buffered_result if isinstance(p, Polygon) and not p.is_empty])

    return polygons if polygons else None


if __name__ == '__main__':
    # Example: A nearly-closed line to test the closure logic.
    # The start and end points are very close.
    line_pts = [(50, 50), (150, 150), (250, 50), (50.05, 50.05)]
    line_widths = [15, 25, 15, 15] # Note: must match number of points

    # --- 1. Generate the polygon vertices ---
    generated_polygons = continious_line_with_variable_width(line_pts, line_widths)

    print("Original Line:", line_pts)
    print("Widths at each point:", line_widths)

    # --- 2. Visualize the result ---
    if not generated_polygons:
        print("\nCould not generate polygon to plot.")
    else:
        print(f"\nGenerated {len(generated_polygons)} polygon(s). Now plotting...")
        # Unpack coordinates for plotting
        line_x, line_y = zip(*line_pts)

        # Create plot
        fig, ax = plt.subplots(figsize=(10, 8))

        # Plot the generated polygons
        for poly in generated_polygons:
            poly_x, poly_y = poly.exterior.xy
            ax.fill(poly_x, poly_y, alpha=0.4, fc='cyan', ec='blue')
        # Add a single legend entry for all generated polygons
        ax.plot([], [], alpha=0.4, color='cyan', label='Generated Polygon(s)')

        # Plot the original line on top
        ax.plot(line_x, line_y, color='red', marker='o', linestyle='-', linewidth=2, label='Original Line')

        # Configure and show the plot
        ax.set_title('Variable-Width Buffer Visualization')
        ax.set_xlabel('X Coordinate')
        ax.set_ylabel('Y Coordinate')
        ax.legend()
        ax.grid(True, linestyle=':', alpha=0.6)
        ax.set_aspect('equal', adjustable='box')  # Crucial for correct aspect ratio
        plt.show()