from typing import Optional
from PySide6.QtCore import <PERSON><PERSON><PERSON>Application
from PySide6.QtWidgets import QFileDialog
from PySide6.QtUiTools import QUiLoader
from Defect.DefectType import DefectType
from Display import plotter_base
from Display.plotter2D import plotter2D
from Display.plotter3D_pyvista import <PERSON>lotter3DPyVista
from GcodeParse import GcodeProcess as GP
from GcodeParse.ModelInfo import LineType, DotType
from Defect import LongEmptyMove, Overhang, LayerTime, Overlap
from UI import style

class Qt_Mainwindow:
# Init and connections
    def __init__(self):
        self.ui = QUiLoader().load('UI/main_ui.ui')
        style.apply_style(self.ui)

        self.model = GP.ModelInfo()
        self.plt_3D = Plotter3DPyVista()
        self.plt_2D = plotter2D()
        
        # A cache to store the results of defect analysis, avoiding re-computation.
        self.defect_results_cache = {}

        self._switch_plt_mode('2D')
        self.init_plotter_connection()

        # Assign a custom close event handler for graceful shutdown
        self.ui.closeEvent = self.closeEvent
        
        # Connect all UI events
        self.ui.combo_plot_layerNum.currentIndexChanged.connect(self.on_combo_plot_layerNum_changed)
        self.ui.pushButton_chooseFile.clicked.connect(self.on_pushButton_chooseFile_clicked)
        self.ui.pushButton_importFile.clicked.connect(self.on_pushButton_importFile_clicked)
        self.ui.pushButton_plot_2D.clicked.connect(self.on_pushButton_plot2D_clicked)
        self.ui.pushButton_plot_3D.clicked.connect(self.on_pushButton_plot3D_clicked)
        self.ui.pushButton_plot_clf.clicked.connect(self.on_pushButton_plot_clf_clicked)
        self.ui.pushButton_analyseDefect.clicked.connect(self.on_pushButton_analyseDefect_clicked)
        self.ui.verticalSlider_plot_layerNum.valueChanged.connect(self.on_verticalSlider_plot_layerNum_valueChanged)
        self.ui.lineEdit_xlim_l.editingFinished.connect(self.on_lineEdit_lim_textChanged)
        self.ui.lineEdit_xlim_h.editingFinished.connect(self.on_lineEdit_lim_textChanged)
        self.ui.lineEdit_ylim_l.editingFinished.connect(self.on_lineEdit_lim_textChanged)
        self.ui.lineEdit_ylim_h.editingFinished.connect(self.on_lineEdit_lim_textChanged)
        self.ui.horizontalScrollBar_plot_trackNum.valueChanged.connect(self.on_plot_settings_changed)
        self.ui.horizontalScrollBar_transparant.valueChanged.connect(self.on_plot_settings_changed)

    def init_plotter_connection(self):
        # Create mapping between LineType and corresponding checkbox names
        self.is_plot_checkbox = {LineType: "checkBox_isPlot_" + LineType.value.replace(" ", "_") for LineType in LineType}
        self.is_plot_checkbox |= {DotType: "checkBox_isPlot_" + DotType.value.replace(" ", "_") for DotType in DotType}
        
        # Connect checkboxes to plot function
        other_checkboxes = [
            "checkBox_isPlot_line_width"
        ]
        checkboxes = other_checkboxes + list(self.is_plot_checkbox.values())
        for checkbox in checkboxes:
            cb = getattr(self.ui, checkbox)
            cb.stateChanged.connect(self.on_plot_settings_changed)
        self.ui.checkBox_isPlot_defect.stateChanged.connect(self.on_toggle_defect_plot)
        radioButtons=[
            "radioButton_plot_color_layer",
            "radioButton_plot_color_type",
            "radioButton_plot_color_width",
            "radioButton_plot_color_time",
            "radioButton_plot_color_defect"
        ]
        for radioButton in radioButtons:
            rb = getattr(self.ui, radioButton)
            rb.clicked.connect(self.on_plot_settings_changed)
        
        # Return initial map (also used for updates)
        self.plt_2D.line_isPlot_MAP = self._update_line_isPlot_MAP()


# Slot functions
    def on_pushButton_chooseFile_clicked(self):
        """Open file dialog to choose G-code file"""
        exist_file_path = self.ui.lineEdit_filePath.text()
        exist_file_path = exist_file_path if exist_file_path else r"E:/模型/gcode和缺陷模型"
        filePath, _ = QFileDialog.getOpenFileName(
            self.ui,  # 父窗口对象
            "选择你要上传的gcode文件",  # 标题
            exist_file_path,  # 起始目录
            "文本类型 (*.txt *.gcode)"  # 选择类型过滤项，过滤内容在括号中
        )
        if filePath:
            self.ui.lineEdit_filePath.setText(filePath)

    def on_pushButton_importFile_clicked(self):
        """Import G-code file and setup plotters"""
        try:
            file_path = self.ui.lineEdit_filePath.text()
            if not file_path:
                self.ui.statusbar.showMessage("请先选择文件", 2000)
                return

            try:
                chord_error_mm = float(self.ui.lineEdit_chord_error.text())
            except ValueError:
                print("Error Input: chord_error_mm should be a float. Now use defalt parameter 0.01mm.")
                chord_error_mm = 0.01       
            self.model = GP.GCodeProcess(file_path).g_file2model(chord_error_mm=chord_error_mm, max_discrete_num=50)
            
            # pre calculate
            for layer in self.model.layers:
                layer.extract_outer_inner_wall_contour()
                layer.extract_z_seam()
            self.model.store_discrete_data()
            
            # Set model for both plotters
            self.plt_3D.set_model(self.model)
            self.plt_2D.set_model(self.model)
            layer_num = len(self.model.layers) - 1
        except Exception as e:
            print(f"Error importing file: {e}")
            self.ui.statusbar.showMessage("❌ 文件导入失败", 2000)

        #Setup model info display
        try:
            self.ui.label_model_info_display.setText(
                f"模型层数：{self.model.paras['total layer number']}\n"
                f"Bambu Studio：{self.model.paras['BambuStudio']}\n"
                f"喷嘴直径：{self.model.paras['nozzle_diameter']}\n"
                f"G1/2/3指令数目:{len([t for layer in self.model.layers for t in layer.tracks])}"
            )
        except KeyError:
            self.ui.statusbar.showMessage("模型信息不完整", 2000)
        else:
            self.ui.statusbar.showMessage("✅ 模型已导入", 2000)

        # Setup UI controls
        self.ui.combo_plot_layerNum.clear()
        self.ui.combo_plot_layerNum.addItems([str(i) for i in range(layer_num + 1)])
        self.ui.verticalSlider_plot_layerNum.setMinimum(0)
        self.ui.verticalSlider_plot_layerNum.setMaximum(layer_num)
        self.ui.verticalSlider_plot_layerNum.setValue(1)    

    def on_combo_plot_layerNum_changed(self):
        self.ui.verticalSlider_plot_layerNum.setValue(self.ui.combo_plot_layerNum.currentIndex())
    
    def on_pushButton_plot2D_clicked(self):
        """Handle 2D plot button"""
        try:
            # Switch to 2D mode if not already
            if not hasattr(self, 'current_plot_mode') or self.current_plot_mode != '2D':
                self._switch_plt_mode('2D')
            if self.current_plot_mode != '2D':
                self.ui.statusbar.showMessage("无法初始化2D模式", 2000)
                return
            
            self._plotter_plot()
        
        except Exception as e:
            print(f"Error in 2D plotting: {e}")
            import traceback
            traceback.print_exc()
            self.ui.statusbar.showMessage("❌ 2D绘制失败", 2000)

    def on_pushButton_plot3D_clicked(self):
        """Handle 3D plot button"""
        try:
            # Switch to 3D mode if not already
            if not hasattr(self, 'current_plot_mode') or self.current_plot_mode != '3D':
                self._switch_plt_mode('3D')
            if self.current_plot_mode != '3D':
                self.ui.statusbar.showMessage("无法初始化3D模式", 2000)
                return
                     
            self.ui.horizontalScrollBar_plot_trackNum.setValue(self.ui.horizontalScrollBar_plot_trackNum.maximum())
            self._plotter_plot()

        except Exception as e:
            print(f"Error in 3D plotting: {e}")
            import traceback
            traceback.print_exc()
            self.ui.statusbar.showMessage("❌ 3D绘制失败", 2000)

    def on_pushButton_analyseDefect_clicked(self):
        defect_type = self._get_defect_mode()
        if defect_type is None:
            self.ui.statusbar.showMessage("Please select a defect type to analyze.", 2000)
            return

        self.ui.statusbar.showMessage(f"Analyzing {defect_type.value}...", 3000)
        
        if defect_type == DefectType.LongEmptyMove:
            max_dist=float(self.ui.lineEdit_max_longEmptyDist.text())
            result = LongEmptyMove.analysis_long_empMove(self.model, max_dist)
            self.defect_results_cache[defect_type] = result
            num_defects = sum(len(vis.drawables) for vis in result)
            self.ui.statusbar.showMessage(f"✅ Found {num_defects} long empty moves on {len(result)} layers.", 2000)
            self.ui.textEdit_defect_display.setText(f"Found {num_defects} long empty moves on {len(result)} layers.")
        
        elif defect_type == DefectType.LargeOverlap:
            # Basic parameters
            r = float(self.ui.lineEdit_overlap_detect_radius.text())
            k = float(self.ui.lineEdit_overlap_adjust_ratio.text())

            # Filter activation flags and parameters from UI
            use_corner_filter = self.ui.checkBox_isUseFilter_sharpCorner.isChecked()
            corner_angle = float(self.ui.lineEdit_para_sharp_ang.text())
            corner_dist = float(self.ui.lineEdit_para_dist_to_angle.text())
            # NOTE: Assumes a QLineEdit named 'lineEdit_para_look_ahead_dist' has been added to the UI.
            corner_look_ahead = float(self.ui.lineEdit_para_look_ahead_dist.text())

            use_density_filter = self.ui.checkBox_isUseFilter_isolatePoint.isChecked()
            isolate_num = int(self.ui.lineEdit_para_isolate_num.text())
            isolate_dist = float(self.ui.lineEdit_para_isolate_dis.text())

            result = Overlap.overlap_detection(
                model=self.model, detect_radius=r, adjust_ratio=k,
                use_corner_filter=use_corner_filter,
                corner_angle_threshold=corner_angle,
                corner_distance_threshold=corner_dist,
                corner_look_ahead_dist=corner_look_ahead,
                use_density_filter=use_density_filter,
                min_cluster_size=isolate_num,
                cluster_distance=isolate_dist
            )
            self.defect_results_cache[defect_type] = result
            self.ui.statusbar.showMessage(f"✅ Overlap analysis complete: Found {len(result)} defects.", 2000)
            self.ui.textEdit_defect_display.setText(f"Found {len(result)} overlap defects across all layers.")
        
        elif defect_type == DefectType.Overhang:
            result = Overhang.overhang_detection(self.model, float(self.ui.lineEdit_min_overlap_ratio.text()))
            self.defect_results_cache[defect_type] = result
            num_segments = sum(len(vis.drawables) for vis in result)
            self.ui.statusbar.showMessage(f"✅ Overhang analysis complete: Found {num_segments} segments.", 2000)
            self.ui.textEdit_defect_display.setText(f"Found overhangs on {len(result)} layers.")
        
        elif defect_type == DefectType.LayerTimeDiff:
            result = LayerTime.analysis_layer_time_diff(self.model)
            self.defect_results_cache[defect_type] = result
            num_defects = len(result.layer_styles)
            self.ui.statusbar.showMessage(f"✅ Layer Time analysis complete: Found {num_defects} layers with time difference.", 2000)
            self.ui.textEdit_defect_display.setText(f"Found {num_defects} layers with significant time difference:\n{list(result.layer_styles.keys())}")

    def on_pushButton_plot_clf_clicked(self):
        """Clear current plot"""
        plotter = self._get_current_plotter()
        plotter.clear_canvas()
        self.ui.statusbar.showMessage("✅ 画布已清空", 1000)
     
    def on_plot_settings_changed(self):
        """Handle plot settings changes for both 2D and 3D modes"""
        plotter = self._get_current_plotter()
        if plotter is None: return

        # This function no longer handles defect toggling.
        # It handles color, visibility, and slider changes.
        can_fast_update = (self.current_plot_mode == '3D' and hasattr(plotter, '_actors_by_type') and plotter._actors_by_type)

        if can_fast_update:
            # FAST PATH for color/visibility changes:
            # The model geometry exists. Just update its properties.
            print("Fast path: Updating color/visibility.")
            plotter.line_isPlot_MAP = self._update_line_isPlot_MAP()
            plotter.update_color(self._get_color_mode())
            plotter.update_visibility()
        else:
            # SLOW PATH: Full replot.
            # This is necessary for 2D mode, the very first plot, or when changing
            # fundamental plot parameters that require rebuilding all geometry.
            print("Slow path: Full replot.")
            self._plotter_plot()

    def on_verticalSlider_plot_layerNum_valueChanged(self):
        # Update combo box to match slider
        self.ui.combo_plot_layerNum.setCurrentIndex(self.ui.verticalSlider_plot_layerNum.value())
        if self.current_plot_mode == '2D' or (self.current_plot_mode == '3D' and self.ui.radioButton_plt_one_layer_3D.isChecked()):
            self._plotter_plot()

    def on_toggle_defect_plot(self):
        """Dedicated slot for toggling defect visibility for performance."""
        plotter = self._get_current_plotter()
        if plotter is None: return
        
        # In 2D mode, a full replot is needed to manage z-order and clearing correctly.
        if self.current_plot_mode == '2D':
            self._plotter_plot()
            return

        # In 3D mode, we can do a fast overlay.
        print("Fast path: Toggling defect visibility.")
        defects_to_plot = self._get_defects_to_plot()
        plotter.plot_defects(defects_to_plot)
        
    def on_lineEdit_lim_textChanged(self):
        """Handle axis limit changes for both 2D and 3D modes"""
        if self.current_plot_mode == '2D':
            # Handle 2D axis limits
            current_lims = self.plt_2D.ax.axis()
            txt_lims = [
                self.ui.lineEdit_xlim_l.text(),  # x下限
                self.ui.lineEdit_xlim_h.text(),  # x上限
                self.ui.lineEdit_ylim_l.text(),  # y下限
                self.ui.lineEdit_ylim_h.text()  # y上限
            ]
            try:
                float_lims = [
                    float(txt_lim) if txt_lim.strip() != "" else current_lims[i]
                    for i, txt_lim in enumerate(txt_lims)
                ]
            except ValueError:
                print("请输入有效的数字")
                return
            self.plt_2D.ax.axis(float_lims)
            self.plt_2D.canvas.draw_idle()

#Private functions
    def _get_color_mode(self):
        """Get color mode based on UI radio button states"""
        from Display.plotter_base import color_mode
        
        if self.ui.radioButton_plot_color_layer.isChecked():
            return color_mode.layer_based
        elif self.ui.radioButton_plot_color_type.isChecked():
            return color_mode.line_type_based
        elif self.ui.radioButton_plot_color_width.isChecked():
            return color_mode.line_width_based
        elif self.ui.radioButton_plot_color_time.isChecked():
            return color_mode.layer_time_based
        elif self.ui.radioButton_plot_color_defect.isChecked():
            return color_mode.defect_based
        else:
            return color_mode.line_type_based      
 
    def _get_defect_mode(self):
        """Get defect mode based on UI radio button states"""
        from Defect.DefectType import DefectType
        
        if self.ui.radioButton_longEmptyDist.isChecked():
            return DefectType.LongEmptyMove
        elif self.ui.radioButton_defect_overlap.isChecked():
            return DefectType.LargeOverlap
        elif self.ui.radioButton_defect_overhang.isChecked():
            return DefectType.Overhang
        elif self.ui.radioButton_defect_layerTimeDiff.isChecked():
            return DefectType.LayerTimeDiff
        elif self.ui.radioButton_overhang_zseam.isChecked():
            return DefectType.ZSeam
        else:
            return None
         
    def _get_defects_to_plot(self) -> list:
        """
        Helper function to get the current defect visualization data based on UI state.
        Filters defects for the current layer if in a single-layer view.
        """
        if not self.ui.checkBox_isPlot_defect.isChecked():
            return []

        defect_type_to_plot = self._get_defect_mode()
        if not defect_type_to_plot or defect_type_to_plot not in self.defect_results_cache:
            return []

        cached_result = self.defect_results_cache[defect_type_to_plot]

        # LayerTimeDiff is a special case that applies styles to layers, not individual drawables.
        # It's best handled by the plotter directly without filtering here.
        if defect_type_to_plot == DefectType.LayerTimeDiff:
            return [cached_result] if cached_result else []

        # Determine if we are in a single-layer view mode
        is_single_layer_view = (self.current_plot_mode == '2D' or
                                (self.current_plot_mode == '3D' and self.ui.radioButton_plt_one_layer_3D.isChecked()))

        if not is_single_layer_view:
            # Full model view, return all defects of the selected type
            return cached_result if isinstance(cached_result, list) else [cached_result]

        # Single layer view, filter defects by the current layer ID
        current_layer_id = self.ui.combo_plot_layerNum.currentIndex()
        defects_for_layer = []

        # The cached result for most defect types is a list of DefectVisualization objects
        if isinstance(cached_result, list):
            for vis_obj in cached_result:
                # Each vis_obj should have metadata with the layer_id
                if vis_obj.metadata and vis_obj.metadata.get('layer_id') == current_layer_id:
                    defects_for_layer.append(vis_obj)

        return defects_for_layer

    def _switch_plt_mode(self, mode: str):
        """Switch plot mode to 2D or 3D"""
        if hasattr(self, 'current_plot_mode') and self.current_plot_mode == mode:
            return

        # Before switching, if the current mode is 3D, we must explicitly
        # close the plotter to release its OpenGL context before it's destroyed.
        if hasattr(self, 'current_plot_mode') and self.current_plot_mode == '3D':
            print("Switching away from 3D mode. Closing 3D plotter to release resources.")
            self.plt_3D.close()

        self.current_plot_mode = mode

        plotter = self._get_current_plotter()
        try:
            plotter.init_widget(self.ui.matplotlib_area)  
            print(f"Switched to {mode} mode")
        except Exception as e:
            print(f"Failed to switch to {mode} mode: {e}")
            if mode != '2D':
                self._switch_plt_mode('2D')

    def _get_current_plotter(self) -> Optional[plotter_base.PlotterBase]:
        """Get the current plotter based on mode"""
        if hasattr(self, 'current_plot_mode') and self.current_plot_mode == '2D':
            return self.plt_2D
        elif hasattr(self, 'current_plot_mode') and self.current_plot_mode == '3D':
            return self.plt_3D
        return None

    def _update_line_isPlot_MAP(self) -> dict:
        # Build the map dynamically based on current checkbox states
        line_isPlot_MAP = {
            type: getattr(self.ui, checkbox_name).isChecked() 
            for type, checkbox_name in self.is_plot_checkbox.items()
        }
        line_isPlot_MAP.update({
            DefectType.LongEmptyMove: True
        })
        return line_isPlot_MAP

    def _validate_model(self) -> bool:
        """Validate that model is loaded and ready"""
        # Check if model is loaded
        if not hasattr(self, 'model') or self.model is None or not hasattr(self.model, 'layers') or len(self.model.layers) == 0:
            print("Model not loaded or invalid")
            self.ui.statusbar.showMessage("请导入正确的模型", 2000)
            return False
        return True

    def _validate_combo_plot_layer_num(self) -> bool:
        """Validate that layer number is valid"""
        # Check if layer number is valid
        layer_num = self.ui.combo_plot_layerNum.currentIndex()
        if self.ui.combo_plot_layerNum.count() == 0:
            print(f"No layer numbers in combo box")
            self.ui.statusbar.showMessage("没有可用层数", 2000)
            return False
        elif layer_num < 0 or layer_num >= len(self.model.layers):
            print(f"Invalid layer number: {layer_num}, total layers: {len(self.model.layers)}")
            self.ui.statusbar.showMessage("没有可用层数", 2000)
            return False
        return True

    def _plotter_plot(self):
        """Plot using the current plotter"""
        # Get current plotter
        plotter = self._get_current_plotter()
        if plotter is None:
            return
        
        # Check if model is loaded and layer number is valid
        if not self._validate_model() or not self._validate_combo_plot_layer_num():
            return
        layer_num = self.ui.combo_plot_layerNum.currentIndex()
        plotter.set_layer_id(layer_num)
        plotter.set_model(self.model)

        # Update filter map if available
        if hasattr(plotter, 'line_isPlot_MAP'):
            plotter.line_isPlot_MAP = self._update_line_isPlot_MAP()
        
        # Update plot settings
        clr_md = self._get_color_mode()
        track_ratio = self.ui.horizontalScrollBar_plot_trackNum.value() / self.ui.horizontalScrollBar_plot_trackNum.maximum()
        alpha = self.ui.horizontalScrollBar_transparant.value() / self.ui.horizontalScrollBar_transparant.maximum()
        
        is_draw_width = self.ui.checkBox_isPlot_line_width.isChecked()
        is_clear_previous = not self.ui.checkBox_isPlot_layer_overlap.isChecked()
        
        # --- 1. Plot base model or one layer ---
        if self.current_plot_mode =='3D':
            if self.ui.radioButton_plt_model_3D.isChecked():
                plotter.plot_model(clr_md=clr_md, alpha=alpha)
            elif self.ui.radioButton_plt_one_layer_3D.isChecked():
                plotter.plot_one_layer(clr_md=clr_md, alpha=alpha, is_clear_previous=is_clear_previous)
        
        elif self.current_plot_mode =='2D':
            plotter.plot_one_layer(clr_md=clr_md,
                                    alpha=alpha,
                                    is_draw_width=is_draw_width,
                                    track_ratio=track_ratio,
                                    is_clear_previous=is_clear_previous)
        
        # --- 2. Universal Defect Plotting ---
        if self.ui.checkBox_isPlot_defect.isChecked():
            defects_to_plot = self._get_defects_to_plot()
            plotter.plot_defects(defects_to_plot)

        # --- 3. Finalize 2D plot ---
        if self.current_plot_mode == '2D':
            # Update axis limits
            self.on_lineEdit_lim_textChanged()
            plotter.canvas.draw_idle()
            QCoreApplication.processEvents()  # 强制刷新界面

#Event
    def closeEvent(self, event):
        """
        Custom close event handler to ensure graceful shutdown of resources, especially the PyVista OpenGL context.
        """

        try:
            # Explicitly close the 3D plotter to release its OpenGL context before the main window is destroyed. This prevents VTK errors.
            if self.plt_3D:
                self.plt_3D.close()
        except Exception as e:
            print(f"Error during 3D plotter cleanup: {e}")
        
        # It's good practice to explicitly accept the event to ensure the window proceeds with closing.
        event.accept()
