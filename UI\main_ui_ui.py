# -*- coding: utf-8 -*-

################################################################################
## Form generated from reading UI file 'main_ui.ui'
##
## Created by: Qt User Interface Compiler version 6.9.1
##
## WARNING! All changes made in this file will be lost when recompiling UI file!
################################################################################

from PySide6.QtCore import (QCoreApplication, QDate, QDateTime, QLocale,
    QMetaObject, QObject, QPoint, QRect,
    QSize, QTime, QUrl, Qt)
from PySide6.QtGui import (Q<PERSON><PERSON>, QColor, Q<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Q<PERSON>ursor,
    <PERSON><PERSON><PERSON>, QFontDatabase, QGradient, QIcon,
    QImage, Q<PERSON>eySequence, QLinearGradient, QPainter,
    QPalette, QPixmap, QRadialGradient, QTransform)
from PySide6.QtWidgets import (QApplication, QCheckBox, QComboBox, QGridLayout,
    QGroupBox, QHBoxLayout, QLabel, QLayout,
    QLineEdit, QMainWindow, QMenuBar, QPushButton,
    QRadioButton, QScrollBar, QSizePolicy, QSlider,
    QStatusBar, QTabWidget, QTextEdit, QWidget)

class Ui_MainWindow(object):
    def setupUi(self, MainWindow):
        if not MainWindow.objectName():
            MainWindow.setObjectName(u"MainWindow")
        MainWindow.resize(1300, 750)
        self.centralwidget = QWidget(MainWindow)
        self.centralwidget.setObjectName(u"centralwidget")
        self.tabWidget = QTabWidget(self.centralwidget)
        self.tabWidget.setObjectName(u"tabWidget")
        self.tabWidget.setGeometry(QRect(0, 0, 1300, 750))
        self.tab = QWidget()
        self.tab.setObjectName(u"tab")
        self.groupBox_4 = QGroupBox(self.tab)
        self.groupBox_4.setObjectName(u"groupBox_4")
        self.groupBox_4.setGeometry(QRect(650, 10, 191, 391))
        self.label_model_info_display = QLabel(self.groupBox_4)
        self.label_model_info_display.setObjectName(u"label_model_info_display")
        self.label_model_info_display.setGeometry(QRect(10, 20, 171, 351))
        self.label_model_info_display.setAlignment(Qt.AlignmentFlag.AlignLeading|Qt.AlignmentFlag.AlignLeft|Qt.AlignmentFlag.AlignTop)
        self.groupBox = QGroupBox(self.tab)
        self.groupBox.setObjectName(u"groupBox")
        self.groupBox.setGeometry(QRect(20, 90, 621, 311))
        self.gridLayoutWidget = QWidget(self.groupBox)
        self.gridLayoutWidget.setObjectName(u"gridLayoutWidget")
        self.gridLayoutWidget.setGeometry(QRect(10, 20, 601, 274))
        self.gridLayout = QGridLayout(self.gridLayoutWidget)
        self.gridLayout.setObjectName(u"gridLayout")
        self.gridLayout.setSizeConstraint(QLayout.SizeConstraint.SetDefaultConstraint)
        self.gridLayout.setContentsMargins(0, 0, 0, 0)
        self.lineEdit_max_longEmptyDist_6 = QLineEdit(self.gridLayoutWidget)
        self.lineEdit_max_longEmptyDist_6.setObjectName(u"lineEdit_max_longEmptyDist_6")

        self.gridLayout.addWidget(self.lineEdit_max_longEmptyDist_6, 9, 5, 1, 1)

        self.radioButton_defect_layerTimeDiff = QRadioButton(self.gridLayoutWidget)
        self.radioButton_defect_layerTimeDiff.setObjectName(u"radioButton_defect_layerTimeDiff")

        self.gridLayout.addWidget(self.radioButton_defect_layerTimeDiff, 9, 0, 1, 1)

        self.lineEdit_overlap_adjust_ratio = QLineEdit(self.gridLayoutWidget)
        self.lineEdit_overlap_adjust_ratio.setObjectName(u"lineEdit_overlap_adjust_ratio")

        self.gridLayout.addWidget(self.lineEdit_overlap_adjust_ratio, 1, 5, 1, 1)

        self.label_5 = QLabel(self.gridLayoutWidget)
        self.label_5.setObjectName(u"label_5")

        self.gridLayout.addWidget(self.label_5, 1, 1, 1, 1)

        self.lineEdit_min_overlap_ratio = QLineEdit(self.gridLayoutWidget)
        self.lineEdit_min_overlap_ratio.setObjectName(u"lineEdit_min_overlap_ratio")

        self.gridLayout.addWidget(self.lineEdit_min_overlap_ratio, 8, 3, 1, 1)

        self.label_13 = QLabel(self.gridLayoutWidget)
        self.label_13.setObjectName(u"label_13")

        self.gridLayout.addWidget(self.label_13, 9, 1, 1, 1)

        self.radioButton_defect_overhang = QRadioButton(self.gridLayoutWidget)
        self.radioButton_defect_overhang.setObjectName(u"radioButton_defect_overhang")

        self.gridLayout.addWidget(self.radioButton_defect_overhang, 8, 0, 1, 1)

        self.label_7 = QLabel(self.gridLayoutWidget)
        self.label_7.setObjectName(u"label_7")

        self.gridLayout.addWidget(self.label_7, 8, 1, 1, 1)

        self.lineEdit_max_longEmptyDist = QLineEdit(self.gridLayoutWidget)
        self.lineEdit_max_longEmptyDist.setObjectName(u"lineEdit_max_longEmptyDist")

        self.gridLayout.addWidget(self.lineEdit_max_longEmptyDist, 0, 3, 1, 1)

        self.label_3 = QLabel(self.gridLayoutWidget)
        self.label_3.setObjectName(u"label_3")

        self.gridLayout.addWidget(self.label_3, 0, 1, 1, 1)

        self.label_15 = QLabel(self.gridLayoutWidget)
        self.label_15.setObjectName(u"label_15")

        self.gridLayout.addWidget(self.label_15, 2, 4, 1, 1)

        self.lineEdit_overlap_detect_radius = QLineEdit(self.gridLayoutWidget)
        self.lineEdit_overlap_detect_radius.setObjectName(u"lineEdit_overlap_detect_radius")

        self.gridLayout.addWidget(self.lineEdit_overlap_detect_radius, 1, 3, 1, 1)

        self.radioButton_defect_overlap = QRadioButton(self.gridLayoutWidget)
        self.radioButton_defect_overlap.setObjectName(u"radioButton_defect_overlap")
        self.radioButton_defect_overlap.setChecked(True)

        self.gridLayout.addWidget(self.radioButton_defect_overlap, 1, 0, 1, 1)

        self.lineEdit_min_overlap_ratio_2 = QLineEdit(self.gridLayoutWidget)
        self.lineEdit_min_overlap_ratio_2.setObjectName(u"lineEdit_min_overlap_ratio_2")

        self.gridLayout.addWidget(self.lineEdit_min_overlap_ratio_2, 8, 5, 1, 1)

        self.lineEdit_para_sharp_ang = QLineEdit(self.gridLayoutWidget)
        self.lineEdit_para_sharp_ang.setObjectName(u"lineEdit_para_sharp_ang")

        self.gridLayout.addWidget(self.lineEdit_para_sharp_ang, 2, 5, 1, 1)

        self.radioButton_longEmptyDist = QRadioButton(self.gridLayoutWidget)
        self.radioButton_longEmptyDist.setObjectName(u"radioButton_longEmptyDist")
        self.radioButton_longEmptyDist.setChecked(False)

        self.gridLayout.addWidget(self.radioButton_longEmptyDist, 0, 0, 1, 1)

        self.radioButton_overhang_zseam = QRadioButton(self.gridLayoutWidget)
        self.radioButton_overhang_zseam.setObjectName(u"radioButton_overhang_zseam")

        self.gridLayout.addWidget(self.radioButton_overhang_zseam, 10, 0, 1, 1)

        self.lineEdit_para_dist_to_angle = QLineEdit(self.gridLayoutWidget)
        self.lineEdit_para_dist_to_angle.setObjectName(u"lineEdit_para_dist_to_angle")

        self.gridLayout.addWidget(self.lineEdit_para_dist_to_angle, 2, 7, 1, 1)

        self.lineEdit_max_longEmptyDist_3 = QLineEdit(self.gridLayoutWidget)
        self.lineEdit_max_longEmptyDist_3.setObjectName(u"lineEdit_max_longEmptyDist_3")

        self.gridLayout.addWidget(self.lineEdit_max_longEmptyDist_3, 9, 3, 1, 1)

        self.label_6 = QLabel(self.gridLayoutWidget)
        self.label_6.setObjectName(u"label_6")

        self.gridLayout.addWidget(self.label_6, 9, 4, 1, 1)

        self.label_16 = QLabel(self.gridLayoutWidget)
        self.label_16.setObjectName(u"label_16")

        self.gridLayout.addWidget(self.label_16, 2, 6, 1, 1)

        self.label_14 = QLabel(self.gridLayoutWidget)
        self.label_14.setObjectName(u"label_14")

        self.gridLayout.addWidget(self.label_14, 8, 4, 1, 1)

        self.label_12 = QLabel(self.gridLayoutWidget)
        self.label_12.setObjectName(u"label_12")

        self.gridLayout.addWidget(self.label_12, 1, 4, 1, 1)

        self.label_18 = QLabel(self.gridLayoutWidget)
        self.label_18.setObjectName(u"label_18")

        self.gridLayout.addWidget(self.label_18, 4, 4, 1, 1)

        self.lineEdit_para_isolate_num = QLineEdit(self.gridLayoutWidget)
        self.lineEdit_para_isolate_num.setObjectName(u"lineEdit_para_isolate_num")

        self.gridLayout.addWidget(self.lineEdit_para_isolate_num, 4, 5, 1, 1)

        self.label_17 = QLabel(self.gridLayoutWidget)
        self.label_17.setObjectName(u"label_17")

        self.gridLayout.addWidget(self.label_17, 4, 6, 1, 1)

        self.lineEdit_para_isolate_dis = QLineEdit(self.gridLayoutWidget)
        self.lineEdit_para_isolate_dis.setObjectName(u"lineEdit_para_isolate_dis")

        self.gridLayout.addWidget(self.lineEdit_para_isolate_dis, 4, 7, 1, 1)

        self.checkBox_isUseFilter_isolatePoint = QCheckBox(self.gridLayoutWidget)
        self.checkBox_isUseFilter_isolatePoint.setObjectName(u"checkBox_isUseFilter_isolatePoint")
        self.checkBox_isUseFilter_isolatePoint.setChecked(True)

        self.gridLayout.addWidget(self.checkBox_isUseFilter_isolatePoint, 4, 1, 1, 1)

        self.checkBox_isUseFilter_sharpCorner = QCheckBox(self.gridLayoutWidget)
        self.checkBox_isUseFilter_sharpCorner.setObjectName(u"checkBox_isUseFilter_sharpCorner")
        self.checkBox_isUseFilter_sharpCorner.setChecked(True)

        self.gridLayout.addWidget(self.checkBox_isUseFilter_sharpCorner, 2, 1, 1, 1)

        self.lineEdit_para_look_ahead_dist = QLineEdit(self.gridLayoutWidget)
        self.lineEdit_para_look_ahead_dist.setObjectName(u"lineEdit_para_look_ahead_dist")

        self.gridLayout.addWidget(self.lineEdit_para_look_ahead_dist, 2, 3, 1, 1)

        self.pushButton_analyseDefect = QPushButton(self.groupBox)
        self.pushButton_analyseDefect.setObjectName(u"pushButton_analyseDefect")
        self.pushButton_analyseDefect.setGeometry(QRect(480, 270, 124, 24))
        self.groupBox_3 = QGroupBox(self.tab)
        self.groupBox_3.setObjectName(u"groupBox_3")
        self.groupBox_3.setGeometry(QRect(20, 10, 621, 71))
        self.horizontalLayoutWidget_2 = QWidget(self.groupBox_3)
        self.horizontalLayoutWidget_2.setObjectName(u"horizontalLayoutWidget_2")
        self.horizontalLayoutWidget_2.setGeometry(QRect(10, 20, 601, 41))
        self.horizontalLayout_2 = QHBoxLayout(self.horizontalLayoutWidget_2)
        self.horizontalLayout_2.setSpacing(4)
        self.horizontalLayout_2.setObjectName(u"horizontalLayout_2")
        self.horizontalLayout_2.setSizeConstraint(QLayout.SizeConstraint.SetDefaultConstraint)
        self.horizontalLayout_2.setContentsMargins(0, 0, 0, 0)
        self.label_2 = QLabel(self.horizontalLayoutWidget_2)
        self.label_2.setObjectName(u"label_2")

        self.horizontalLayout_2.addWidget(self.label_2)

        self.lineEdit_filePath = QLineEdit(self.horizontalLayoutWidget_2)
        self.lineEdit_filePath.setObjectName(u"lineEdit_filePath")

        self.horizontalLayout_2.addWidget(self.lineEdit_filePath)

        self.pushButton_chooseFile = QPushButton(self.horizontalLayoutWidget_2)
        self.pushButton_chooseFile.setObjectName(u"pushButton_chooseFile")

        self.horizontalLayout_2.addWidget(self.pushButton_chooseFile)

        self.pushButton_importFile = QPushButton(self.horizontalLayoutWidget_2)
        self.pushButton_importFile.setObjectName(u"pushButton_importFile")

        self.horizontalLayout_2.addWidget(self.pushButton_importFile)

        self.label_19 = QLabel(self.horizontalLayoutWidget_2)
        self.label_19.setObjectName(u"label_19")

        self.horizontalLayout_2.addWidget(self.label_19)

        self.lineEdit_chord_error = QLineEdit(self.horizontalLayoutWidget_2)
        self.lineEdit_chord_error.setObjectName(u"lineEdit_chord_error")

        self.horizontalLayout_2.addWidget(self.lineEdit_chord_error)

        self.groupBox_11 = QGroupBox(self.tab)
        self.groupBox_11.setObjectName(u"groupBox_11")
        self.groupBox_11.setGeometry(QRect(20, 410, 821, 251))
        self.textEdit_defect_display = QTextEdit(self.groupBox_11)
        self.textEdit_defect_display.setObjectName(u"textEdit_defect_display")
        self.textEdit_defect_display.setGeometry(QRect(10, 20, 801, 311))
        self.tabWidget.addTab(self.tab, "")
        self.tab_2 = QWidget()
        self.tab_2.setObjectName(u"tab_2")
        self.matplotlib_area = QWidget(self.tab_2)
        self.matplotlib_area.setObjectName(u"matplotlib_area")
        self.matplotlib_area.setGeometry(QRect(420, 10, 850, 650))
        self.groupBox_2 = QGroupBox(self.tab_2)
        self.groupBox_2.setObjectName(u"groupBox_2")
        self.groupBox_2.setGeometry(QRect(10, 0, 401, 71))
        self.horizontalLayoutWidget = QWidget(self.groupBox_2)
        self.horizontalLayoutWidget.setObjectName(u"horizontalLayoutWidget")
        self.horizontalLayoutWidget.setGeometry(QRect(10, 20, 381, 41))
        self.horizontalLayout = QHBoxLayout(self.horizontalLayoutWidget)
        self.horizontalLayout.setObjectName(u"horizontalLayout")
        self.horizontalLayout.setContentsMargins(0, 0, 0, 0)
        self.label = QLabel(self.horizontalLayoutWidget)
        self.label.setObjectName(u"label")

        self.horizontalLayout.addWidget(self.label)

        self.combo_plot_layerNum = QComboBox(self.horizontalLayoutWidget)
        self.combo_plot_layerNum.setObjectName(u"combo_plot_layerNum")

        self.horizontalLayout.addWidget(self.combo_plot_layerNum)

        self.pushButton_plot_2D = QPushButton(self.horizontalLayoutWidget)
        self.pushButton_plot_2D.setObjectName(u"pushButton_plot_2D")

        self.horizontalLayout.addWidget(self.pushButton_plot_2D)

        self.pushButton_plot_clf = QPushButton(self.horizontalLayoutWidget)
        self.pushButton_plot_clf.setObjectName(u"pushButton_plot_clf")

        self.horizontalLayout.addWidget(self.pushButton_plot_clf)

        self.horizontalScrollBar_plot_trackNum = QScrollBar(self.tab_2)
        self.horizontalScrollBar_plot_trackNum.setObjectName(u"horizontalScrollBar_plot_trackNum")
        self.horizontalScrollBar_plot_trackNum.setGeometry(QRect(420, 660, 851, 20))
        self.horizontalScrollBar_plot_trackNum.setMaximum(1000)
        self.horizontalScrollBar_plot_trackNum.setValue(1000)
        self.horizontalScrollBar_plot_trackNum.setSliderPosition(1000)
        self.horizontalScrollBar_plot_trackNum.setOrientation(Qt.Orientation.Horizontal)
        self.groupBox_5 = QGroupBox(self.tab_2)
        self.groupBox_5.setObjectName(u"groupBox_5")
        self.groupBox_5.setGeometry(QRect(10, 80, 401, 231))
        self.horizontalLayoutWidget_3 = QWidget(self.groupBox_5)
        self.horizontalLayoutWidget_3.setObjectName(u"horizontalLayoutWidget_3")
        self.horizontalLayoutWidget_3.setGeometry(QRect(10, 10, 381, 221))
        self.gridLayout_2 = QGridLayout(self.horizontalLayoutWidget_3)
        self.gridLayout_2.setObjectName(u"gridLayout_2")
        self.gridLayout_2.setContentsMargins(0, 0, 0, 0)
        self.checkBox_isPlot_Outer_wall = QCheckBox(self.horizontalLayoutWidget_3)
        self.checkBox_isPlot_Outer_wall.setObjectName(u"checkBox_isPlot_Outer_wall")
        self.checkBox_isPlot_Outer_wall.setChecked(True)

        self.gridLayout_2.addWidget(self.checkBox_isPlot_Outer_wall, 0, 1, 1, 1)

        self.checkBox_isPlot_Inner_wall = QCheckBox(self.horizontalLayoutWidget_3)
        self.checkBox_isPlot_Inner_wall.setObjectName(u"checkBox_isPlot_Inner_wall")
        self.checkBox_isPlot_Inner_wall.setChecked(True)

        self.gridLayout_2.addWidget(self.checkBox_isPlot_Inner_wall, 0, 0, 1, 1)

        self.checkBox_isPlot_Top_surface = QCheckBox(self.horizontalLayoutWidget_3)
        self.checkBox_isPlot_Top_surface.setObjectName(u"checkBox_isPlot_Top_surface")
        self.checkBox_isPlot_Top_surface.setChecked(True)

        self.gridLayout_2.addWidget(self.checkBox_isPlot_Top_surface, 2, 0, 1, 1)

        self.checkBox_isPlot_Bottom_surface = QCheckBox(self.horizontalLayoutWidget_3)
        self.checkBox_isPlot_Bottom_surface.setObjectName(u"checkBox_isPlot_Bottom_surface")
        self.checkBox_isPlot_Bottom_surface.setChecked(True)

        self.gridLayout_2.addWidget(self.checkBox_isPlot_Bottom_surface, 2, 1, 1, 1)

        self.checkBox_isPlot_Overhang_wall = QCheckBox(self.horizontalLayoutWidget_3)
        self.checkBox_isPlot_Overhang_wall.setObjectName(u"checkBox_isPlot_Overhang_wall")
        self.checkBox_isPlot_Overhang_wall.setChecked(True)

        self.gridLayout_2.addWidget(self.checkBox_isPlot_Overhang_wall, 0, 2, 1, 1)

        self.checkBox_isPlot_Floating_vertical_shell = QCheckBox(self.horizontalLayoutWidget_3)
        self.checkBox_isPlot_Floating_vertical_shell.setObjectName(u"checkBox_isPlot_Floating_vertical_shell")
        self.checkBox_isPlot_Floating_vertical_shell.setChecked(True)

        self.gridLayout_2.addWidget(self.checkBox_isPlot_Floating_vertical_shell, 6, 0, 1, 1)

        self.checkBox_isPlot_Brim = QCheckBox(self.horizontalLayoutWidget_3)
        self.checkBox_isPlot_Brim.setObjectName(u"checkBox_isPlot_Brim")
        self.checkBox_isPlot_Brim.setChecked(True)

        self.gridLayout_2.addWidget(self.checkBox_isPlot_Brim, 2, 2, 1, 1)

        self.checkBox_isPlot_Gap_infill = QCheckBox(self.horizontalLayoutWidget_3)
        self.checkBox_isPlot_Gap_infill.setObjectName(u"checkBox_isPlot_Gap_infill")
        self.checkBox_isPlot_Gap_infill.setChecked(True)

        self.gridLayout_2.addWidget(self.checkBox_isPlot_Gap_infill, 3, 2, 1, 1)

        self.checkBox_isPlot_Sparse_infill = QCheckBox(self.horizontalLayoutWidget_3)
        self.checkBox_isPlot_Sparse_infill.setObjectName(u"checkBox_isPlot_Sparse_infill")
        self.checkBox_isPlot_Sparse_infill.setChecked(True)

        self.gridLayout_2.addWidget(self.checkBox_isPlot_Sparse_infill, 3, 0, 1, 1)

        self.checkBox_isPlot_Internal_solid_infill = QCheckBox(self.horizontalLayoutWidget_3)
        self.checkBox_isPlot_Internal_solid_infill.setObjectName(u"checkBox_isPlot_Internal_solid_infill")
        self.checkBox_isPlot_Internal_solid_infill.setChecked(True)

        self.gridLayout_2.addWidget(self.checkBox_isPlot_Internal_solid_infill, 3, 1, 1, 1)

        self.checkBox_isPlot_Support = QCheckBox(self.horizontalLayoutWidget_3)
        self.checkBox_isPlot_Support.setObjectName(u"checkBox_isPlot_Support")
        self.checkBox_isPlot_Support.setChecked(True)

        self.gridLayout_2.addWidget(self.checkBox_isPlot_Support, 4, 0, 1, 1)

        self.checkBox_isPlot_Support_interface = QCheckBox(self.horizontalLayoutWidget_3)
        self.checkBox_isPlot_Support_interface.setObjectName(u"checkBox_isPlot_Support_interface")
        self.checkBox_isPlot_Support_interface.setChecked(True)

        self.gridLayout_2.addWidget(self.checkBox_isPlot_Support_interface, 4, 1, 1, 1)

        self.checkBox_isPlot_Bridge = QCheckBox(self.horizontalLayoutWidget_3)
        self.checkBox_isPlot_Bridge.setObjectName(u"checkBox_isPlot_Bridge")
        self.checkBox_isPlot_Bridge.setChecked(True)

        self.gridLayout_2.addWidget(self.checkBox_isPlot_Bridge, 5, 0, 1, 1)

        self.checkBox_isPlot_Support_transition = QCheckBox(self.horizontalLayoutWidget_3)
        self.checkBox_isPlot_Support_transition.setObjectName(u"checkBox_isPlot_Support_transition")
        self.checkBox_isPlot_Support_transition.setChecked(True)

        self.gridLayout_2.addWidget(self.checkBox_isPlot_Support_transition, 4, 2, 1, 1)

        self.checkBox_isPlot_Empty_move = QCheckBox(self.horizontalLayoutWidget_3)
        self.checkBox_isPlot_Empty_move.setObjectName(u"checkBox_isPlot_Empty_move")

        self.gridLayout_2.addWidget(self.checkBox_isPlot_Empty_move, 5, 1, 1, 1)

        self.checkBox_isPlot_Wipe = QCheckBox(self.horizontalLayoutWidget_3)
        self.checkBox_isPlot_Wipe.setObjectName(u"checkBox_isPlot_Wipe")

        self.gridLayout_2.addWidget(self.checkBox_isPlot_Wipe, 5, 2, 1, 1)

        self.checkBox_isPlot_Custom = QCheckBox(self.horizontalLayoutWidget_3)
        self.checkBox_isPlot_Custom.setObjectName(u"checkBox_isPlot_Custom")

        self.gridLayout_2.addWidget(self.checkBox_isPlot_Custom, 6, 1, 1, 1)

        self.groupBox_6 = QGroupBox(self.tab_2)
        self.groupBox_6.setObjectName(u"groupBox_6")
        self.groupBox_6.setGeometry(QRect(10, 440, 401, 91))
        self.label_4 = QLabel(self.groupBox_6)
        self.label_4.setObjectName(u"label_4")
        self.label_4.setGeometry(QRect(20, 50, 51, 31))
        self.horizontalScrollBar_transparant = QScrollBar(self.groupBox_6)
        self.horizontalScrollBar_transparant.setObjectName(u"horizontalScrollBar_transparant")
        self.horizontalScrollBar_transparant.setGeometry(QRect(80, 56, 311, 20))
        self.horizontalScrollBar_transparant.setMaximum(100)
        self.horizontalScrollBar_transparant.setValue(100)
        self.horizontalScrollBar_transparant.setOrientation(Qt.Orientation.Horizontal)
        self.horizontalLayoutWidget_5 = QWidget(self.groupBox_6)
        self.horizontalLayoutWidget_5.setObjectName(u"horizontalLayoutWidget_5")
        self.horizontalLayoutWidget_5.setGeometry(QRect(10, 20, 391, 31))
        self.horizontalLayout_3 = QHBoxLayout(self.horizontalLayoutWidget_5)
        self.horizontalLayout_3.setObjectName(u"horizontalLayout_3")
        self.horizontalLayout_3.setContentsMargins(0, 0, 0, 0)
        self.checkBox_isPlot_line_width = QCheckBox(self.horizontalLayoutWidget_5)
        self.checkBox_isPlot_line_width.setObjectName(u"checkBox_isPlot_line_width")
        self.checkBox_isPlot_line_width.setChecked(False)

        self.horizontalLayout_3.addWidget(self.checkBox_isPlot_line_width)

        self.checkBox_isPlot_layer_overlap = QCheckBox(self.horizontalLayoutWidget_5)
        self.checkBox_isPlot_layer_overlap.setObjectName(u"checkBox_isPlot_layer_overlap")
        self.checkBox_isPlot_layer_overlap.setChecked(False)

        self.horizontalLayout_3.addWidget(self.checkBox_isPlot_layer_overlap)

        self.checkBox_isPlot_defect = QCheckBox(self.horizontalLayoutWidget_5)
        self.checkBox_isPlot_defect.setObjectName(u"checkBox_isPlot_defect")
        self.checkBox_isPlot_defect.setChecked(False)

        self.horizontalLayout_3.addWidget(self.checkBox_isPlot_defect)

        self.groupBox_7 = QGroupBox(self.tab_2)
        self.groupBox_7.setObjectName(u"groupBox_7")
        self.groupBox_7.setGeometry(QRect(10, 370, 401, 61))
        self.gridLayoutWidget_3 = QWidget(self.groupBox_7)
        self.gridLayoutWidget_3.setObjectName(u"gridLayoutWidget_3")
        self.gridLayoutWidget_3.setGeometry(QRect(10, 10, 381, 51))
        self.gridLayout_4 = QGridLayout(self.gridLayoutWidget_3)
        self.gridLayout_4.setObjectName(u"gridLayout_4")
        self.gridLayout_4.setContentsMargins(0, 0, 0, 0)
        self.radioButton_plot_color_width = QRadioButton(self.gridLayoutWidget_3)
        self.radioButton_plot_color_width.setObjectName(u"radioButton_plot_color_width")

        self.gridLayout_4.addWidget(self.radioButton_plot_color_width, 0, 2, 1, 1)

        self.radioButton_plot_color_defect = QRadioButton(self.gridLayoutWidget_3)
        self.radioButton_plot_color_defect.setObjectName(u"radioButton_plot_color_defect")

        self.gridLayout_4.addWidget(self.radioButton_plot_color_defect, 0, 4, 1, 1)

        self.radioButton_plot_color_type = QRadioButton(self.gridLayoutWidget_3)
        self.radioButton_plot_color_type.setObjectName(u"radioButton_plot_color_type")
        self.radioButton_plot_color_type.setChecked(True)

        self.gridLayout_4.addWidget(self.radioButton_plot_color_type, 0, 1, 1, 1)

        self.radioButton_plot_color_time = QRadioButton(self.gridLayoutWidget_3)
        self.radioButton_plot_color_time.setObjectName(u"radioButton_plot_color_time")

        self.gridLayout_4.addWidget(self.radioButton_plot_color_time, 0, 3, 1, 1)

        self.radioButton_plot_color_layer = QRadioButton(self.gridLayoutWidget_3)
        self.radioButton_plot_color_layer.setObjectName(u"radioButton_plot_color_layer")

        self.gridLayout_4.addWidget(self.radioButton_plot_color_layer, 0, 0, 1, 1)

        self.groupBox_8 = QGroupBox(self.tab_2)
        self.groupBox_8.setObjectName(u"groupBox_8")
        self.groupBox_8.setGeometry(QRect(10, 310, 401, 61))
        self.gridLayoutWidget_2 = QWidget(self.groupBox_8)
        self.gridLayoutWidget_2.setObjectName(u"gridLayoutWidget_2")
        self.gridLayoutWidget_2.setGeometry(QRect(10, 10, 381, 51))
        self.gridLayout_5 = QGridLayout(self.gridLayoutWidget_2)
        self.gridLayout_5.setObjectName(u"gridLayout_5")
        self.gridLayout_5.setContentsMargins(0, 0, 0, 0)
        self.checkBox_isPlot_Seam = QCheckBox(self.gridLayoutWidget_2)
        self.checkBox_isPlot_Seam.setObjectName(u"checkBox_isPlot_Seam")
        self.checkBox_isPlot_Seam.setChecked(True)

        self.gridLayout_5.addWidget(self.checkBox_isPlot_Seam, 0, 1, 1, 1)

        self.checkBox_isPlot_Reload = QCheckBox(self.gridLayoutWidget_2)
        self.checkBox_isPlot_Reload.setObjectName(u"checkBox_isPlot_Reload")

        self.gridLayout_5.addWidget(self.checkBox_isPlot_Reload, 0, 0, 1, 1)

        self.checkBox_isPlot_AdjustPara = QCheckBox(self.gridLayoutWidget_2)
        self.checkBox_isPlot_AdjustPara.setObjectName(u"checkBox_isPlot_AdjustPara")

        self.gridLayout_5.addWidget(self.checkBox_isPlot_AdjustPara, 0, 2, 1, 1)

        self.checkBox_isPlot_Withdraw = QCheckBox(self.gridLayoutWidget_2)
        self.checkBox_isPlot_Withdraw.setObjectName(u"checkBox_isPlot_Withdraw")
        self.checkBox_isPlot_Withdraw.setChecked(False)

        self.gridLayout_5.addWidget(self.checkBox_isPlot_Withdraw, 0, 3, 1, 1)

        self.groupBox_9 = QGroupBox(self.tab_2)
        self.groupBox_9.setObjectName(u"groupBox_9")
        self.groupBox_9.setGeometry(QRect(10, 540, 401, 61))
        self.horizontalLayoutWidget_7 = QWidget(self.groupBox_9)
        self.horizontalLayoutWidget_7.setObjectName(u"horizontalLayoutWidget_7")
        self.horizontalLayoutWidget_7.setGeometry(QRect(10, 20, 381, 31))
        self.horizontalLayout_5 = QHBoxLayout(self.horizontalLayoutWidget_7)
        self.horizontalLayout_5.setObjectName(u"horizontalLayout_5")
        self.horizontalLayout_5.setContentsMargins(0, 0, 0, 0)
        self.label_8 = QLabel(self.horizontalLayoutWidget_7)
        self.label_8.setObjectName(u"label_8")

        self.horizontalLayout_5.addWidget(self.label_8)

        self.lineEdit_xlim_l = QLineEdit(self.horizontalLayoutWidget_7)
        self.lineEdit_xlim_l.setObjectName(u"lineEdit_xlim_l")

        self.horizontalLayout_5.addWidget(self.lineEdit_xlim_l)

        self.label_10 = QLabel(self.horizontalLayoutWidget_7)
        self.label_10.setObjectName(u"label_10")

        self.horizontalLayout_5.addWidget(self.label_10)

        self.lineEdit_xlim_h = QLineEdit(self.horizontalLayoutWidget_7)
        self.lineEdit_xlim_h.setObjectName(u"lineEdit_xlim_h")

        self.horizontalLayout_5.addWidget(self.lineEdit_xlim_h)

        self.label_9 = QLabel(self.horizontalLayoutWidget_7)
        self.label_9.setObjectName(u"label_9")

        self.horizontalLayout_5.addWidget(self.label_9)

        self.lineEdit_ylim_l = QLineEdit(self.horizontalLayoutWidget_7)
        self.lineEdit_ylim_l.setObjectName(u"lineEdit_ylim_l")

        self.horizontalLayout_5.addWidget(self.lineEdit_ylim_l)

        self.label_11 = QLabel(self.horizontalLayoutWidget_7)
        self.label_11.setObjectName(u"label_11")

        self.horizontalLayout_5.addWidget(self.label_11)

        self.lineEdit_ylim_h = QLineEdit(self.horizontalLayoutWidget_7)
        self.lineEdit_ylim_h.setObjectName(u"lineEdit_ylim_h")

        self.horizontalLayout_5.addWidget(self.lineEdit_ylim_h)

        self.verticalSlider_plot_layerNum = QSlider(self.tab_2)
        self.verticalSlider_plot_layerNum.setObjectName(u"verticalSlider_plot_layerNum")
        self.verticalSlider_plot_layerNum.setGeometry(QRect(1280, 10, 16, 650))
        self.verticalSlider_plot_layerNum.setMaximum(1000)
        self.verticalSlider_plot_layerNum.setValue(0)
        self.verticalSlider_plot_layerNum.setOrientation(Qt.Orientation.Vertical)
        self.verticalSlider_plot_layerNum.setTickInterval(0)
        self.groupBox_10 = QGroupBox(self.tab_2)
        self.groupBox_10.setObjectName(u"groupBox_10")
        self.groupBox_10.setGeometry(QRect(10, 610, 401, 61))
        self.horizontalLayoutWidget_4 = QWidget(self.groupBox_10)
        self.horizontalLayoutWidget_4.setObjectName(u"horizontalLayoutWidget_4")
        self.horizontalLayoutWidget_4.setGeometry(QRect(10, 20, 381, 41))
        self.horizontalLayout_4 = QHBoxLayout(self.horizontalLayoutWidget_4)
        self.horizontalLayout_4.setObjectName(u"horizontalLayout_4")
        self.horizontalLayout_4.setContentsMargins(0, 0, 0, 0)
        self.pushButton_plot_3D = QPushButton(self.horizontalLayoutWidget_4)
        self.pushButton_plot_3D.setObjectName(u"pushButton_plot_3D")

        self.horizontalLayout_4.addWidget(self.pushButton_plot_3D)

        self.radioButton_plt_one_layer_3D = QRadioButton(self.horizontalLayoutWidget_4)
        self.radioButton_plt_one_layer_3D.setObjectName(u"radioButton_plt_one_layer_3D")
        self.radioButton_plt_one_layer_3D.setChecked(True)

        self.horizontalLayout_4.addWidget(self.radioButton_plt_one_layer_3D)

        self.radioButton_plt_model_3D = QRadioButton(self.horizontalLayoutWidget_4)
        self.radioButton_plt_model_3D.setObjectName(u"radioButton_plt_model_3D")

        self.horizontalLayout_4.addWidget(self.radioButton_plt_model_3D)

        self.checkBox_isRender_whole_model = QCheckBox(self.horizontalLayoutWidget_4)
        self.checkBox_isRender_whole_model.setObjectName(u"checkBox_isRender_whole_model")
        self.checkBox_isRender_whole_model.setChecked(False)

        self.horizontalLayout_4.addWidget(self.checkBox_isRender_whole_model)

        self.tabWidget.addTab(self.tab_2, "")
        MainWindow.setCentralWidget(self.centralwidget)
        self.menubar = QMenuBar(MainWindow)
        self.menubar.setObjectName(u"menubar")
        self.menubar.setGeometry(QRect(0, 0, 1300, 33))
        MainWindow.setMenuBar(self.menubar)
        self.statusbar = QStatusBar(MainWindow)
        self.statusbar.setObjectName(u"statusbar")
        MainWindow.setStatusBar(self.statusbar)

        self.retranslateUi(MainWindow)

        self.tabWidget.setCurrentIndex(0)


        QMetaObject.connectSlotsByName(MainWindow)
    # setupUi

    def retranslateUi(self, MainWindow):
        MainWindow.setWindowTitle(QCoreApplication.translate("MainWindow", u"MainWindow", None))
        self.groupBox_4.setTitle(QCoreApplication.translate("MainWindow", u"\u6a21\u578b\u4fe1\u606f", None))
        self.label_model_info_display.setText("")
        self.groupBox.setTitle(QCoreApplication.translate("MainWindow", u"\u7f3a\u9677\u68c0\u6d4b", None))
        self.lineEdit_max_longEmptyDist_6.setText(QCoreApplication.translate("MainWindow", u"0.4", None))
        self.radioButton_defect_layerTimeDiff.setText(QCoreApplication.translate("MainWindow", u"\u5c42\u65f6\u95f4\u5dee\u5f02", None))
        self.lineEdit_overlap_adjust_ratio.setText(QCoreApplication.translate("MainWindow", u"0.02", None))
        self.label_5.setText(QCoreApplication.translate("MainWindow", u"\u68c0\u6d4b\u534a\u5f84/mm", None))
        self.lineEdit_min_overlap_ratio.setText(QCoreApplication.translate("MainWindow", u"0.2", None))
        self.label_13.setText(QCoreApplication.translate("MainWindow", u"\u7a81\u53d8\u503c/s", None))
        self.radioButton_defect_overhang.setText(QCoreApplication.translate("MainWindow", u"\u60ac\u5782\u68c0\u6d4b", None))
        self.label_7.setText(QCoreApplication.translate("MainWindow", u"\u91cd\u53e0\u6bd4\u4f8b", None))
        self.lineEdit_max_longEmptyDist.setText(QCoreApplication.translate("MainWindow", u"240", None))
        self.label_3.setText(QCoreApplication.translate("MainWindow", u"\u8ddd\u79bb\u9608\u503c/mm", None))
        self.label_15.setText(QCoreApplication.translate("MainWindow", u"\u9510\u89d2\u89d2\u5ea6", None))
        self.lineEdit_overlap_detect_radius.setText(QCoreApplication.translate("MainWindow", u"1.2", None))
        self.radioButton_defect_overlap.setText(QCoreApplication.translate("MainWindow", u"\u5185\u5899\u5806\u6599", None))
        self.lineEdit_min_overlap_ratio_2.setText(QCoreApplication.translate("MainWindow", u"50", None))
        self.lineEdit_para_sharp_ang.setText(QCoreApplication.translate("MainWindow", u"100", None))
        self.radioButton_longEmptyDist.setText(QCoreApplication.translate("MainWindow", u"\u957f\u8ddd\u79bb\u7a7a\u9a76     ", None))
        self.radioButton_overhang_zseam.setText(QCoreApplication.translate("MainWindow", u"\u60ac\u5782\u63a5\u7f1d", None))
        self.lineEdit_para_dist_to_angle.setText(QCoreApplication.translate("MainWindow", u"1", None))
        self.lineEdit_max_longEmptyDist_3.setText(QCoreApplication.translate("MainWindow", u"25", None))
        self.label_6.setText(QCoreApplication.translate("MainWindow", u"\u7a81\u53d8\u6bd4\u4f8b", None))
        self.label_16.setText(QCoreApplication.translate("MainWindow", u"\u79bb\u89d2\u8ddd\u79bb", None))
        self.label_14.setText(QCoreApplication.translate("MainWindow", u"\u5c0f\u60ac\u5782\u957f\u5ea6/mm", None))
        self.label_12.setText(QCoreApplication.translate("MainWindow", u"\u8c03\u6574\u7cfb\u6570", None))
        self.label_18.setText(QCoreApplication.translate("MainWindow", u"\u5b64\u70b9\u6570\u76ee", None))
        self.lineEdit_para_isolate_num.setText(QCoreApplication.translate("MainWindow", u"3", None))
        self.label_17.setText(QCoreApplication.translate("MainWindow", u"\u5b64\u70b9\u8ddd\u79bb", None))
        self.lineEdit_para_isolate_dis.setText(QCoreApplication.translate("MainWindow", u"0.6", None))
        self.checkBox_isUseFilter_isolatePoint.setText(QCoreApplication.translate("MainWindow", u"\u5b64\u70b9\u8fc7\u6ee4", None))
        self.checkBox_isUseFilter_sharpCorner.setText(QCoreApplication.translate("MainWindow", u"\u9510\u89d2\u8fc7\u6ee4", None))
        self.lineEdit_para_look_ahead_dist.setText(QCoreApplication.translate("MainWindow", u"2", None))
        self.pushButton_analyseDefect.setText(QCoreApplication.translate("MainWindow", u"\u68c0\u6d4b", None))
        self.groupBox_3.setTitle(QCoreApplication.translate("MainWindow", u"G-code\u6587\u4ef6\u5bfc\u5165", None))
        self.label_2.setText(QCoreApplication.translate("MainWindow", u"\u8def\u5f84\uff1a", None))
        self.pushButton_chooseFile.setText(QCoreApplication.translate("MainWindow", u"\u9009\u62e9\u6587\u4ef6", None))
        self.pushButton_importFile.setText(QCoreApplication.translate("MainWindow", u"\u5bfc\u5165", None))
        self.label_19.setText(QCoreApplication.translate("MainWindow", u"G2/3\u79bb\u6563\u5f13\u9ad8\u8bef\u5dee(mm)\uff1a", None))
        self.lineEdit_chord_error.setText(QCoreApplication.translate("MainWindow", u"0.01", None))
        self.groupBox_11.setTitle(QCoreApplication.translate("MainWindow", u"\u7f3a\u9677\u68c0\u6d4b\u7ed3\u679c", None))
        self.tabWidget.setTabText(self.tabWidget.indexOf(self.tab), QCoreApplication.translate("MainWindow", u"G-code\u6587\u4ef6", None))
        self.groupBox_2.setTitle(QCoreApplication.translate("MainWindow", u"2D\u7ed8\u56fe", None))
        self.label.setText(QCoreApplication.translate("MainWindow", u"\u5c42\u6570\uff1a", None))
        self.pushButton_plot_2D.setText(QCoreApplication.translate("MainWindow", u"2D\u7ed8\u5236", None))
        self.pushButton_plot_clf.setText(QCoreApplication.translate("MainWindow", u"\u6e05\u7a7a", None))
        self.groupBox_5.setTitle(QCoreApplication.translate("MainWindow", u"\u7ebf\u578b", None))
        self.checkBox_isPlot_Outer_wall.setText(QCoreApplication.translate("MainWindow", u"\u5916\u5899", None))
        self.checkBox_isPlot_Inner_wall.setText(QCoreApplication.translate("MainWindow", u"\u5185\u5899", None))
        self.checkBox_isPlot_Top_surface.setText(QCoreApplication.translate("MainWindow", u"\u9876\u9762", None))
        self.checkBox_isPlot_Bottom_surface.setText(QCoreApplication.translate("MainWindow", u"\u5e95\u9762", None))
        self.checkBox_isPlot_Overhang_wall.setText(QCoreApplication.translate("MainWindow", u"\u60ac\u7a7a\u5899", None))
        self.checkBox_isPlot_Floating_vertical_shell.setText(QCoreApplication.translate("MainWindow", u"\u6d6e\u7a7a\u5782\u76f4\u58f3", None))
        self.checkBox_isPlot_Brim.setText(QCoreApplication.translate("MainWindow", u"Brim", None))
        self.checkBox_isPlot_Gap_infill.setText(QCoreApplication.translate("MainWindow", u"\u586b\u7f1d", None))
        self.checkBox_isPlot_Sparse_infill.setText(QCoreApplication.translate("MainWindow", u"\u7a00\u758f\u586b\u5145", None))
        self.checkBox_isPlot_Internal_solid_infill.setText(QCoreApplication.translate("MainWindow", u"\u5b9e\u5fc3\u586b\u5145", None))
        self.checkBox_isPlot_Support.setText(QCoreApplication.translate("MainWindow", u"\u652f\u6491", None))
        self.checkBox_isPlot_Support_interface.setText(QCoreApplication.translate("MainWindow", u"\u652f\u6491\u9762", None))
        self.checkBox_isPlot_Bridge.setText(QCoreApplication.translate("MainWindow", u"\u6865\u63a5", None))
        self.checkBox_isPlot_Support_transition.setText(QCoreApplication.translate("MainWindow", u"\u652f\u6491\u8f6c\u6362\u9762", None))
        self.checkBox_isPlot_Empty_move.setText(QCoreApplication.translate("MainWindow", u"\u7a7a\u9a76", None))
        self.checkBox_isPlot_Wipe.setText(QCoreApplication.translate("MainWindow", u"\u64e6\u62ed", None))
        self.checkBox_isPlot_Custom.setText(QCoreApplication.translate("MainWindow", u"\u81ea\u5b9a\u4e49", None))
        self.groupBox_6.setTitle(QCoreApplication.translate("MainWindow", u"\u663e\u793a\u8bbe\u7f6e", None))
        self.label_4.setText(QCoreApplication.translate("MainWindow", u"\u900f\u660e\u5ea6\uff1a", None))
        self.checkBox_isPlot_line_width.setText(QCoreApplication.translate("MainWindow", u"\u663e\u793a\u7ebf\u5bbd", None))
        self.checkBox_isPlot_layer_overlap.setText(QCoreApplication.translate("MainWindow", u"\u53e0\u5c42\u7ed8\u5236", None))
        self.checkBox_isPlot_defect.setText(QCoreApplication.translate("MainWindow", u"\u663e\u793a\u7f3a\u9677", None))
        self.groupBox_7.setTitle(QCoreApplication.translate("MainWindow", u"\u989c\u8272\u8bbe\u7f6e", None))
        self.radioButton_plot_color_width.setText(QCoreApplication.translate("MainWindow", u"\u7ebf\u5bbd", None))
        self.radioButton_plot_color_defect.setText(QCoreApplication.translate("MainWindow", u"\u7070\u6a21", None))
        self.radioButton_plot_color_type.setText(QCoreApplication.translate("MainWindow", u"\u7ebf\u578b", None))
        self.radioButton_plot_color_time.setText(QCoreApplication.translate("MainWindow", u"\u5c42\u65f6\u95f4", None))
        self.radioButton_plot_color_layer.setText(QCoreApplication.translate("MainWindow", u"\u6574\u5c42", None))
        self.groupBox_8.setTitle(QCoreApplication.translate("MainWindow", u"\u70b9", None))
        self.checkBox_isPlot_Seam.setText(QCoreApplication.translate("MainWindow", u"\u63a5\u7f1d", None))
        self.checkBox_isPlot_Reload.setText(QCoreApplication.translate("MainWindow", u"\u88c5\u586b", None))
        self.checkBox_isPlot_AdjustPara.setText(QCoreApplication.translate("MainWindow", u"\u8c03\u6574", None))
        self.checkBox_isPlot_Withdraw.setText(QCoreApplication.translate("MainWindow", u"\u56de\u62bd", None))
        self.groupBox_9.setTitle(QCoreApplication.translate("MainWindow", u"2D\u5750\u6807\u8303\u56f4", None))
        self.label_8.setText(QCoreApplication.translate("MainWindow", u"x:", None))
        self.lineEdit_xlim_l.setText("")
        self.label_10.setText(QCoreApplication.translate("MainWindow", u"-", None))
        self.lineEdit_xlim_h.setText("")
        self.label_9.setText(QCoreApplication.translate("MainWindow", u"y:", None))
        self.lineEdit_ylim_l.setText("")
        self.label_11.setText(QCoreApplication.translate("MainWindow", u"-", None))
        self.lineEdit_ylim_h.setText("")
        self.groupBox_10.setTitle(QCoreApplication.translate("MainWindow", u"3D\u7ed8\u56fe", None))
        self.pushButton_plot_3D.setText(QCoreApplication.translate("MainWindow", u"3D\u7ed8\u5236", None))
        self.radioButton_plt_one_layer_3D.setText(QCoreApplication.translate("MainWindow", u"\u5355\u5c42", None))
        self.radioButton_plt_model_3D.setText(QCoreApplication.translate("MainWindow", u"\u6a21\u578b", None))
        self.checkBox_isRender_whole_model.setText(QCoreApplication.translate("MainWindow", u"\u5168\u90e8\u6e32\u67d3", None))
        self.tabWidget.setTabText(self.tabWidget.indexOf(self.tab_2), QCoreApplication.translate("MainWindow", u"\u7ed8\u56fe", None))
    # retranslateUi

