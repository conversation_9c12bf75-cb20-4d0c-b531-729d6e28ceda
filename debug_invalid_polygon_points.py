import matplotlib.pyplot as plt
import numpy as np
import os

def plot_invalid_polygon(filepath):
    """Reads a text file of points and plots them to find issues."""
    if not os.path.exists(filepath):
        print(f"File not found: {filepath}")
        return

    try:
        # Load points, skipping the header comment
        points = np.loadtxt(filepath, delimiter=',', comments='#')
    except Exception as e:
        print(f"Could not read file {filepath}: {e}")
        return

    if points.shape[0] < 3:
        print(f"Not enough points in {filepath} to form a polygon.")
        return

    x, y = points[:, 0], points[:, 1]

    # Create the plot
    plt.figure(figsize=(10, 8))
    
    # Plot the path of the contour
    plt.plot(x, y, 'b-', marker='o', markersize=2, linewidth=1, label='Contour Path')
    
    # Highlight the start and end points to see if it's closed
    plt.plot(x[0], y[0], 'go', markersize=10, label='Start Point')
    plt.plot(x[-1], y[-1], 'ro', markersize=10, label='End Point')

    plt.title(f"Visualization of Points from\n{os.path.basename(filepath)}")
    plt.xlabel("X coordinate")
    plt.ylabel("Y coordinate")
    plt.legend()
    plt.grid(True)
    plt.axis('equal') # This is crucial for seeing the true shape
    plt.show()

# --- Plot the two files you provided ---
plot_invalid_polygon('e:\Software\PyCharm Community Edition 2025.1.2\SCRIPTS\g-code-defect\invalid_polygon_points_20250807_173511_538522.txt')
plot_invalid_polygon('e:\Software\PyCharm Community Edition 2025.1.2\SCRIPTS\g-code-defect\invalid_polygon_points_20250807_173448_589770.txt')

